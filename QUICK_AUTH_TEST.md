# 🔧 اختبار سريع للمصادقة

## للاختبار الفوري بدون انتظار رفع الملفات:

### 1. **اذهب إلى صفحة تسجيل الدخول**
```
https://exquisite-valkyrie-4b6295.netlify.app/login.html
```

### 2. **افتح Developer Tools**
- اضغط `F12` أو `Ctrl+Shift+I`
- اذهب إلى تبويب `Console`

### 3. **انسخ والصق هذا الكود**

```javascript
// اختبار شامل للمصادقة
async function testAuth() {
    console.log('🔍 بدء اختبار المصادقة...');
    
    try {
        // 1. اختبار الاتصال مع Supabase
        console.log('📡 اختبار الاتصال مع Supabase...');
        if (!window.supabase) {
            throw new Error('❌ Supabase غير محمل');
        }
        console.log('✅ Supabase محمل بنجاح');
        
        // 2. التحقق من الجلسة الحالية
        console.log('👤 التحقق من المستخدم الحالي...');
        const { data: { session } } = await window.supabase.auth.getSession();
        if (session) {
            console.log('✅ يوجد مستخدم مسجل دخول:', session.user.email);
        } else {
            console.log('ℹ️ لا يوجد مستخدم مسجل دخول');
        }
        
        // 3. اختبار تسجيل الدخول
        console.log('🔐 اختبار تسجيل الدخول...');
        const testEmail = '<EMAIL>';
        const testPassword = prompt('أدخل كلمة المرور للحساب <EMAIL>:');
        
        if (testPassword) {
            const { data, error } = await window.supabase.auth.signInWithPassword({
                email: testEmail,
                password: testPassword
            });
            
            if (error) {
                console.error('❌ خطأ في تسجيل الدخول:', error.message);
                
                // رسائل خطأ مخصصة
                if (error.message.includes('Invalid login credentials')) {
                    console.log('💡 الحل: تحقق من البريد الإلكتروني وكلمة المرور');
                } else if (error.message.includes('Email not confirmed')) {
                    console.log('💡 الحل: سنحاول تأكيد البريد الإلكتروني...');
                    
                    // محاولة تأكيد البريد الإلكتروني
                    try {
                        await fetch('https://anjbnmqgxzhebvfszwaa.supabase.co/rest/v1/rpc/confirm_user_email', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'apikey': window.SUPABASE_ANON_KEY
                            },
                            body: JSON.stringify({ user_email: testEmail })
                        });
                        console.log('✅ تم تأكيد البريد الإلكتروني، جرب تسجيل الدخول مرة أخرى');
                    } catch (e) {
                        console.log('💡 يرجى إنشاء حساب جديد');
                    }
                } else if (error.message.includes('Too many requests')) {
                    console.log('💡 الحل: انتظر دقيقة ثم حاول مرة أخرى');
                }
            } else {
                console.log('✅ تم تسجيل الدخول بنجاح!');
                console.log('👤 المستخدم:', data.user.email);
                console.log('🎯 يمكنك الآن الانتقال للوحة التحكم');
                
                // إعادة توجيه تلقائية
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ عام:', error.message);
    }
}

// تشغيل الاختبار
testAuth();
```

### 4. **اختبار إنشاء حساب جديد**

```javascript
// اختبار إنشاء حساب جديد
async function testSignup() {
    console.log('📝 اختبار إنشاء حساب جديد...');
    
    try {
        const email = prompt('أدخل بريد إلكتروني جديد:');
        const password = prompt('أدخل كلمة مرور (6 أحرف على الأقل):');
        
        if (email && password) {
            const { data, error } = await window.supabase.auth.signUp({
                email: email,
                password: password
            });
            
            if (error) {
                console.error('❌ خطأ في إنشاء الحساب:', error.message);
                
                if (error.message.includes('User already registered')) {
                    console.log('💡 هذا البريد مسجل مسبقاً، جرب تسجيل الدخول');
                } else if (error.message.includes('Password should be at least')) {
                    console.log('💡 كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                }
            } else {
                console.log('✅ تم إنشاء الحساب بنجاح!');
                console.log('👤 المستخدم الجديد:', data.user.email);
                console.log('🎯 يمكنك الآن الانتقال للوحة التحكم');
                
                // إعادة توجيه تلقائية
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ عام:', error.message);
    }
}

// تشغيل اختبار التسجيل
testSignup();
```

### 5. **اختبار سريع للإعدادات**

```javascript
// فحص إعدادات Supabase
async function checkSettings() {
    console.log('⚙️ فحص إعدادات Supabase...');
    
    try {
        // فحص المتغيرات
        console.log('🔑 SUPABASE_URL:', window.SUPABASE_URL);
        console.log('🔑 SUPABASE_ANON_KEY:', window.SUPABASE_ANON_KEY ? 'موجود' : 'غير موجود');
        
        // فحص حالة الاتصال
        const { data, error } = await window.supabase.auth.getSession();
        console.log('📡 حالة الجلسة:', error ? 'خطأ' : 'متصل');
        
        if (data.session) {
            console.log('👤 المستخدم الحالي:', data.session.user.email);
        }
        
    } catch (error) {
        console.error('❌ خطأ في فحص الإعدادات:', error.message);
    }
}

checkSettings();
```

## 🎯 خطوات الاختبار:

### الخطوة 1:
1. اذهب إلى: https://exquisite-valkyrie-4b6295.netlify.app/login.html
2. افتح Developer Tools (F12)
3. انسخ والصق كود "اختبار شامل للمصادقة"

### الخطوة 2:
- إذا نجح تسجيل الدخول ✅ - المشكلة محلولة!
- إذا فشل ❌ - ستظهر رسالة واضحة بالمشكلة والحل

### الخطوة 3:
- جرب كود "اختبار إنشاء حساب جديد" إذا كنت تريد حساب جديد

## 🔍 النتائج المتوقعة:

### إذا كان كل شيء يعمل:
```
✅ Supabase محمل بنجاح
✅ تم تسجيل الدخول بنجاح!
👤 المستخدم: <EMAIL>
🎯 يمكنك الآن الانتقال للوحة التحكم
```

### إذا كانت هناك مشكلة:
```
❌ خطأ في تسجيل الدخول: [رسالة الخطأ]
💡 الحل: [خطوات الحل]
```

## 📞 إذا احتجت مساعدة:

أخبرني بالضبط ما تراه في Console وسأساعدك في حل المشكلة فوراً!
