<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المصادقة - منصة سندان</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #3498db;
        }
        
        button {
            width: 100%;
            padding: 15px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            margin-bottom: 10px;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 30px;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            background: #ecf0f1;
            border: none;
            cursor: pointer;
            font-weight: 600;
        }
        
        .tab.active {
            background: #3498db;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 اختبار المصادقة</h1>
        
        <div id="message" class="message"></div>
        
        <div class="tabs">
            <button class="tab active" onclick="switchTab('login')">تسجيل الدخول</button>
            <button class="tab" onclick="switchTab('signup')">إنشاء حساب</button>
        </div>
        
        <!-- تسجيل الدخول -->
        <div id="login-tab" class="tab-content active">
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">البريد الإلكتروني</label>
                    <input type="email" id="loginEmail" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="loginPassword">كلمة المرور</label>
                    <input type="password" id="loginPassword" name="password" required>
                </div>
                
                <button type="submit">تسجيل الدخول</button>
            </form>
        </div>
        
        <!-- إنشاء حساب -->
        <div id="signup-tab" class="tab-content">
            <form id="signupForm">
                <div class="form-group">
                    <label for="signupEmail">البريد الإلكتروني</label>
                    <input type="email" id="signupEmail" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="signupPassword">كلمة المرور</label>
                    <input type="password" id="signupPassword" name="password" required>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">تأكيد كلمة المرور</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                </div>
                
                <button type="submit">إنشاء حساب</button>
            </form>
        </div>
        
        <button onclick="testConnection()">اختبار الاتصال مع Supabase</button>
        <button onclick="checkCurrentUser()">التحقق من المستخدم الحالي</button>
        <button onclick="logout()">تسجيل الخروج</button>
    </div>

    <script src="js/config.js"></script>
    <script src="js/auth-fix.js"></script>
    
    <script>
        function switchTab(tab) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tabBtn => {
                tabBtn.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tab + '-tab').classList.add('active');
            event.target.classList.add('active');
        }
        
        function showMessage(text, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            
            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }
        
        async function testConnection() {
            try {
                showMessage('جاري اختبار الاتصال...', 'info');
                
                if (!window.supabase) {
                    throw new Error('Supabase غير محمل');
                }
                
                // اختبار بسيط للاتصال
                const { data, error } = await window.supabase.auth.getSession();
                
                if (error) {
                    throw error;
                }
                
                showMessage('✅ الاتصال مع Supabase يعمل بشكل صحيح', 'success');
                
            } catch (error) {
                console.error('خطأ في الاتصال:', error);
                showMessage(`❌ خطأ في الاتصال: ${error.message}`, 'error');
            }
        }
        
        async function checkCurrentUser() {
            try {
                const { data: { user } } = await window.supabase.auth.getUser();
                
                if (user) {
                    showMessage(`✅ المستخدم الحالي: ${user.email}`, 'success');
                } else {
                    showMessage('ℹ️ لا يوجد مستخدم مسجل دخول', 'info');
                }
                
            } catch (error) {
                console.error('خطأ في التحقق من المستخدم:', error);
                showMessage(`❌ خطأ: ${error.message}`, 'error');
            }
        }
        
        async function logout() {
            try {
                const { error } = await window.supabase.auth.signOut();
                
                if (error) {
                    throw error;
                }
                
                showMessage('✅ تم تسجيل الخروج بنجاح', 'success');
                
            } catch (error) {
                console.error('خطأ في تسجيل الخروج:', error);
                showMessage(`❌ خطأ: ${error.message}`, 'error');
            }
        }
        
        // اختبار الاتصال عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
