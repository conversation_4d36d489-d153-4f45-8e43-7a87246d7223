/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2c3e50;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
    color: #666;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(44, 62, 80, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #2c3e50;
    border: 2px solid #2c3e50;
}

.btn-secondary:hover {
    background: #2c3e50;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #3498db;
    border: 2px solid #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
}

.btn-full {
    width: 100%;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.nav-brand i {
    margin-left: 10px;
    color: #d4af37;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #d4af37;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #2c3e50;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #2c3e50, #d4af37);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #666;
}

.hero-features {
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.feature-item i {
    color: #27ae60;
    margin-left: 10px;
    font-size: 1.2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Dashboard Preview */
.dashboard-preview {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.dashboard-preview:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.preview-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.preview-dots {
    display: flex;
    gap: 8px;
}

.preview-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #dee2e6;
}

.preview-dots span:first-child {
    background: #e74c3c;
}

.preview-dots span:nth-child(2) {
    background: #f39c12;
}

.preview-dots span:last-child {
    background: #27ae60;
}

.preview-content {
    display: flex;
    height: 300px;
}

.preview-sidebar {
    width: 200px;
    background: #2c3e50;
    padding: 20px 0;
}

.sidebar-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #bdc3c7;
    transition: all 0.3s ease;
    cursor: pointer;
}

.sidebar-item.active {
    background: #34495e;
    color: white;
    border-right: 3px solid #d4af37;
}

.sidebar-item i {
    margin-left: 12px;
    width: 20px;
}

.preview-main {
    flex: 1;
    padding: 20px;
    background: #f8f9fa;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: white;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Pricing Section */
.pricing {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    position: relative;
}

.pricing-card:hover {
    transform: translateY(-10px);
}

.pricing-card.featured {
    border: 3px solid #d4af37;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -15px;
    right: 20px;
    background: #d4af37;
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.pricing-header {
    text-align: center;
    margin-bottom: 2rem;
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 1rem;
}

.currency {
    font-size: 1.2rem;
    color: #666;
    margin-left: 5px;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
}

.period {
    font-size: 1rem;
    color: #666;
    margin-right: 5px;
}

.pricing-features {
    margin-bottom: 2rem;
}

.pricing-features .feature {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem 0;
}

.pricing-features .feature i {
    color: #27ae60;
    margin-left: 10px;
    font-size: 1.1rem;
}

/* Demo Section */
.demo {
    padding: 80px 0;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.demo-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.demo-text h2 {
    color: white;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.demo-text p {
    color: #bdc3c7;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.demo-features {
    margin-bottom: 2rem;
}

.demo-feature {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.demo-feature i {
    color: #d4af37;
    margin-left: 15px;
    font-size: 1.2rem;
}

.demo-feature span {
    color: #ecf0f1;
}

.demo-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.signup-form h3 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.form-note {
    font-size: 0.9rem;
    color: #666;
    text-align: center;
    margin-top: 1rem;
}

.form-note a {
    color: #3498db;
    text-decoration: none;
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background: white;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.testimonial-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-content p {
    font-style: italic;
    font-size: 1.1rem;
    color: #2c3e50;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.author-info h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.author-info span {
    color: #666;
    font-size: 0.9rem;
}

.rating {
    display: flex;
    gap: 2px;
}

.rating i {
    color: #f39c12;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.contact-info p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

/* Special styling for contact methods without icons */
.contact-method > div:only-child {
    width: 100%;
    padding-left: 0;
}

.contact-method i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.contact-method h4 {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.contact-method p {
    color: #666;
    margin: 0;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-brand i {
    margin-left: 10px;
    color: #d4af37;
    font-size: 1.8rem;
}

.footer-section h4 {
    color: white;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #d4af37;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #34495e;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: #d4af37;
}

.footer-bottom {
    border-top: 1px solid #34495e;
    padding-top: 2rem;
    text-align: center;
    color: #bdc3c7;
}

/* WhatsApp Button Styles */
.whatsapp-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #25D366;
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(37, 211, 102, 0.3);
}

.whatsapp-btn:hover {
    background: #128C7E;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    color: white;
    text-decoration: none;
}

.whatsapp-btn i {
    font-size: 1.2em;
}

/* Contact method adjustments for WhatsApp */
.contact-method .whatsapp-btn {
    margin-top: 5px;
}

/* Email Button Styles */
.email-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #007bff;
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
}

.email-btn:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
    color: white;
    text-decoration: none;
}

.email-btn i {
    font-size: 1.2em;
}

/* Contact method adjustments for Email */
.contact-method .email-btn {
    margin-top: 5px;
}

/* Floating WhatsApp Button */
.whatsapp-float {
    position: fixed;
    width: 60px;
    height: 60px;
    bottom: 40px;
    left: 40px;
    background-color: #25D366;
    color: white;
    border-radius: 50px;
    text-align: center;
    font-size: 30px;
    box-shadow: 2px 2px 3px #999;
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    background-color: #128C7E;
    transform: scale(1.1);
    color: white;
    text-decoration: none;
}

.whatsapp-float i {
    margin: 0;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: 2rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .demo-content {
        grid-template-columns: 1fr;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .preview-content {
        flex-direction: column;
        height: auto;
    }
    
    .preview-sidebar {
        width: 100%;
    }
    
    .sidebar-item {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero-text h1 {
        font-size: 1.8rem;
    }
    
    .hero-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .pricing-card.featured {
        transform: none;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}