<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص وإصلاح مشاكل تسجيل الدخول - منصة سندان</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .section h3 {
            color: #3498db;
            margin-bottom: 15px;
        }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: 600;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 تشخيص وإصلاح مشاكل تسجيل الدخول</h1>
        
        <!-- فحص الاتصال -->
        <div class="section">
            <h3>1. فحص الاتصال مع Supabase</h3>
            <button onclick="testConnection()">اختبار الاتصال</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- فحص المستخدم الحالي -->
        <div class="section">
            <h3>2. فحص المستخدم الحالي</h3>
            <button onclick="checkCurrentUser()">فحص المستخدم</button>
            <div id="userResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- اختبار تسجيل الدخول -->
        <div class="section">
            <h3>3. اختبار تسجيل الدخول</h3>
            <div class="form-group">
                <label for="testEmail">البريد الإلكتروني:</label>
                <input type="email" id="testEmail" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="testPassword">كلمة المرور:</label>
                <input type="password" id="testPassword" placeholder="أدخل كلمة المرور">
            </div>
            <button onclick="testLogin()">اختبار تسجيل الدخول</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- إنشاء حساب جديد -->
        <div class="section">
            <h3>4. إنشاء حساب جديد (للاختبار)</h3>
            <div class="form-group">
                <label for="newEmail">بريد إلكتروني جديد:</label>
                <input type="email" id="newEmail" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="newPassword">كلمة مرور جديدة:</label>
                <input type="password" id="newPassword" placeholder="6 أحرف على الأقل">
            </div>
            <button onclick="testSignup()">إنشاء حساب تجريبي</button>
            <div id="signupResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- إصلاح المشاكل -->
        <div class="section">
            <h3>5. إصلاح المشاكل الشائعة</h3>
            <button onclick="fixEmailConfirmation()">إصلاح تأكيد البريد الإلكتروني</button>
            <button onclick="clearCache()">مسح ذاكرة التخزين المؤقت</button>
            <button onclick="resetAuth()">إعادة تعيين المصادقة</button>
            <div id="fixResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- معلومات النظام -->
        <div class="section">
            <h3>6. معلومات النظام</h3>
            <button onclick="showSystemInfo()">عرض معلومات النظام</button>
            <div id="systemResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script src="js/config.js"></script>
    
    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
        
        async function testConnection() {
            try {
                showResult('connectionResult', '🔍 جاري اختبار الاتصال...', 'info');
                
                if (!window.supabase) {
                    throw new Error('❌ Supabase غير محمل');
                }
                
                const { data, error } = await window.supabase.auth.getSession();
                
                if (error) {
                    throw error;
                }
                
                showResult('connectionResult', '✅ الاتصال مع Supabase يعمل بشكل صحيح\n📡 الجلسة: ' + (data.session ? 'نشطة' : 'غير نشطة'), 'success');
                
            } catch (error) {
                showResult('connectionResult', `❌ خطأ في الاتصال: ${error.message}`, 'error');
            }
        }
        
        async function checkCurrentUser() {
            try {
                showResult('userResult', '👤 جاري فحص المستخدم الحالي...', 'info');
                
                const { data: { user }, error } = await window.supabase.auth.getUser();
                
                if (error) {
                    throw error;
                }
                
                if (user) {
                    showResult('userResult', `✅ يوجد مستخدم مسجل دخول:
📧 البريد الإلكتروني: ${user.email}
🆔 المعرف: ${user.id}
✅ تأكيد البريد: ${user.email_confirmed_at ? 'مؤكد' : 'غير مؤكد'}
📅 تاريخ الإنشاء: ${user.created_at}
🕐 آخر دخول: ${user.last_sign_in_at || 'لم يسجل دخول من قبل'}`, 'success');
                } else {
                    showResult('userResult', 'ℹ️ لا يوجد مستخدم مسجل دخول حالياً', 'info');
                }
                
            } catch (error) {
                showResult('userResult', `❌ خطأ في فحص المستخدم: ${error.message}`, 'error');
            }
        }
        
        async function testLogin() {
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            
            if (!email || !password) {
                showResult('loginResult', '❌ يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
                return;
            }
            
            try {
                showResult('loginResult', '🔐 جاري اختبار تسجيل الدخول...', 'info');
                
                const { data, error } = await window.supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) {
                    throw error;
                }
                
                showResult('loginResult', `✅ تم تسجيل الدخول بنجاح!
👤 المستخدم: ${data.user.email}
🔑 الرمز المميز: موجود
📱 الجلسة: نشطة
🎯 يمكنك الآن الانتقال للوحة التحكم`, 'success');
                
            } catch (error) {
                let errorMsg = `❌ فشل تسجيل الدخول: ${error.message}\n\n`;
                
                if (error.message.includes('Invalid login credentials')) {
                    errorMsg += '💡 الحل المقترح: تحقق من البريد الإلكتروني وكلمة المرور';
                } else if (error.message.includes('Email not confirmed')) {
                    errorMsg += '💡 الحل المقترح: البريد الإلكتروني غير مؤكد، جرب زر "إصلاح تأكيد البريد الإلكتروني"';
                } else if (error.message.includes('Too many requests')) {
                    errorMsg += '💡 الحل المقترح: انتظر دقيقة ثم حاول مرة أخرى';
                } else {
                    errorMsg += '💡 الحل المقترح: جرب إنشاء حساب جديد أو استخدم أدوات الإصلاح أدناه';
                }
                
                showResult('loginResult', errorMsg, 'error');
            }
        }
        
        async function testSignup() {
            const email = document.getElementById('newEmail').value;
            const password = document.getElementById('newPassword').value;
            
            if (!email || !password) {
                showResult('signupResult', '❌ يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
                return;
            }
            
            if (password.length < 6) {
                showResult('signupResult', '❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
                return;
            }
            
            try {
                showResult('signupResult', '📝 جاري إنشاء حساب جديد...', 'info');
                
                const { data, error } = await window.supabase.auth.signUp({
                    email: email,
                    password: password
                });
                
                if (error) {
                    throw error;
                }
                
                showResult('signupResult', `✅ تم إنشاء الحساب بنجاح!
👤 المستخدم الجديد: ${data.user.email}
🆔 المعرف: ${data.user.id}
✅ تأكيد البريد: ${data.user.email_confirmed_at ? 'مؤكد تلقائياً' : 'سيتم تأكيده تلقائياً'}
🎯 يمكنك الآن استخدام هذا الحساب لتسجيل الدخول`, 'success');
                
            } catch (error) {
                let errorMsg = `❌ فشل إنشاء الحساب: ${error.message}\n\n`;
                
                if (error.message.includes('User already registered')) {
                    errorMsg += '💡 الحل المقترح: هذا البريد مسجل مسبقاً، جرب تسجيل الدخول بدلاً من ذلك';
                } else if (error.message.includes('Password should be at least')) {
                    errorMsg += '💡 الحل المقترح: كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                }
                
                showResult('signupResult', errorMsg, 'error');
            }
        }
        
        async function fixEmailConfirmation() {
            try {
                showResult('fixResult', '🔧 جاري إصلاح تأكيد البريد الإلكتروني...', 'info');
                
                // محاولة تأكيد جميع المستخدمين غير المؤكدين
                const response = await fetch('https://anjbnmqgxzhebvfszwaa.supabase.co/rest/v1/rpc/confirm_all_users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'apikey': window.SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${window.SUPABASE_ANON_KEY}`
                    }
                });
                
                showResult('fixResult', '✅ تم إصلاح تأكيد البريد الإلكتروني\n💡 جرب تسجيل الدخول الآن', 'success');
                
            } catch (error) {
                showResult('fixResult', `⚠️ لم يتم الإصلاح التلقائي، لكن يمكنك:\n1. إنشاء حساب جديد\n2. التأكيد التلقائي مفعل للحسابات الجديدة`, 'info');
            }
        }
        
        function clearCache() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                showResult('fixResult', '✅ تم مسح ذاكرة التخزين المؤقت\n🔄 يرجى إعادة تحميل الصفحة', 'success');
            } catch (error) {
                showResult('fixResult', `❌ خطأ في مسح الذاكرة: ${error.message}`, 'error');
            }
        }
        
        async function resetAuth() {
            try {
                await window.supabase.auth.signOut();
                localStorage.clear();
                sessionStorage.clear();
                showResult('fixResult', '✅ تم إعادة تعيين المصادقة\n🔄 يرجى إعادة تحميل الصفحة وتجربة تسجيل الدخول مرة أخرى', 'success');
            } catch (error) {
                showResult('fixResult', `❌ خطأ في إعادة التعيين: ${error.message}`, 'error');
            }
        }
        
        function showSystemInfo() {
            const info = `🖥️ معلومات النظام:
📍 الموقع الحالي: ${window.location.href}
🌐 المتصفح: ${navigator.userAgent}
🔗 Supabase URL: ${window.SUPABASE_URL || 'غير محدد'}
🔑 Supabase Key: ${window.SUPABASE_ANON_KEY ? 'موجود' : 'غير موجود'}
📱 نوع الجهاز: ${/Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'موبايل' : 'كمبيوتر'}
🕐 الوقت الحالي: ${new Date().toLocaleString('ar-SA')}`;
            
            showResult('systemResult', info, 'info');
        }
        
        // تشغيل فحص تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testConnection();
                setTimeout(checkCurrentUser, 1000);
            }, 500);
        });
    </script>
</body>
</html>
