// API Functions for Law Office Management System
class LawOfficeAPI {
    constructor() {
        this.client = null;
        this.currentUser = null;
        this.currentOffice = null;
    }

    // Initialize API
    async init() {
        this.client = window.API.getClient();
        if (!this.client) {
            throw new Error('Supabase client not initialized');
        }
        
        // Check if user is logged in
        const { user } = await this.client.auth.getUser();
        if (user) {
            this.currentUser = user;
            await this.loadCurrentOffice();
        }
        
        return this;
    }

    // Authentication methods
    async signUp(email, password, userData) {
        const { data, error } = await this.client.auth.signUp({
            email,
            password,
            options: {
                data: userData
            }
        });
        
        if (error) throw error;
        return data;
    }

    async signIn(email, password) {
        const { data, error } = await this.client.auth.signInWithPassword({
            email,
            password
        });

        if (error) {
            // رسائل خطأ مخصصة
            if (error.message.includes('Email not confirmed')) {
                throw new Error('يرجى تأكيد البريد الإلكتروني أولاً. إذا لم تتلق رسالة التأكيد، يرجى إنشاء حساب جديد.');
            } else if (error.message.includes('Invalid login credentials')) {
                throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة');
            } else if (error.message.includes('Too many requests')) {
                throw new Error('تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً');
            } else {
                throw error;
            }
        }

        this.currentUser = data.user;
        await this.loadCurrentOffice();
        return data;
    }

    async signOut() {
        const { error } = await this.client.auth.signOut();
        if (error) throw error;
        
        this.currentUser = null;
        this.currentOffice = null;
    }

    // Load current user's law office
    async loadCurrentOffice() {
        if (!this.currentUser) return null;

        const { data, error } = await this.client
            .from('lawyers')
            .select(`
                *,
                law_offices (*)
            `)
            .eq('user_id', this.currentUser.id)
            .single();

        if (error) throw error;
        
        this.currentOffice = data.law_offices;
        return this.currentOffice;
    }

    // Law Office methods
    async createLawOffice(officeData) {
        const { data, error } = await this.client
            .from('law_offices')
            .insert(officeData)
            .select()
            .single();

        if (error) throw error;
        return data;
    }

    async updateLawOffice(updates) {
        if (!this.currentOffice) throw new Error('No current office');

        const { data, error } = await this.client
            .from('law_offices')
            .update(updates)
            .eq('id', this.currentOffice.id)
            .select()
            .single();

        if (error) throw error;
        
        this.currentOffice = data;
        return data;
    }

    // Client methods
    async getClients(filters = {}) {
        if (!this.currentOffice) throw new Error('No current office');

        let query = this.client
            .from('clients')
            .select('*')
            .eq('law_office_id', this.currentOffice.id);

        // Apply filters
        Object.entries(filters).forEach(([key, value]) => {
            if (value) query = query.ilike(key, `%${value}%`);
        });

        const { data, error } = await query.order('created_at', { ascending: false });
        if (error) throw error;
        return data;
    }

    async createClient(clientData) {
        if (!this.currentOffice) throw new Error('No current office');

        const { data, error } = await this.client
            .from('clients')
            .insert({
                ...clientData,
                law_office_id: this.currentOffice.id,
                created_by: this.currentUser.id
            })
            .select()
            .single();

        if (error) throw error;
        
        // Log activity
        await this.logActivity('client_created', `تم إضافة عميل جديد: ${clientData.name}`, 'clients', data.id);
        
        return data;
    }

    async updateClient(clientId, updates) {
        const { data, error } = await this.client
            .from('clients')
            .update(updates)
            .eq('id', clientId)
            .select()
            .single();

        if (error) throw error;
        
        // Log activity
        await this.logActivity('client_updated', `تم تحديث بيانات العميل: ${data.name}`, 'clients', clientId);
        
        return data;
    }

    async deleteClient(clientId) {
        const { error } = await this.client
            .from('clients')
            .delete()
            .eq('id', clientId);

        if (error) throw error;
        
        // Log activity
        await this.logActivity('client_deleted', 'تم حذف عميل', 'clients', clientId);
    }

    // Case methods
    async getCases(filters = {}) {
        if (!this.currentOffice) throw new Error('No current office');

        let query = this.client
            .from('cases')
            .select(`
                *,
                clients (name),
                assigned_lawyer:lawyers!cases_assigned_to_fkey (name)
            `)
            .eq('law_office_id', this.currentOffice.id);

        // Apply filters
        Object.entries(filters).forEach(([key, value]) => {
            if (value) {
                if (key === 'status') {
                    query = query.eq(key, value);
                } else {
                    query = query.ilike(key, `%${value}%`);
                }
            }
        });

        const { data, error } = await query.order('created_at', { ascending: false });
        if (error) throw error;
        return data;
    }

    async createCase(caseData) {
        if (!this.currentOffice) throw new Error('No current office');

        const { data, error } = await this.client
            .from('cases')
            .insert({
                ...caseData,
                law_office_id: this.currentOffice.id,
                created_by: this.currentUser.id
            })
            .select()
            .single();

        if (error) throw error;
        
        // Log activity
        await this.logActivity('case_created', `تم إنشاء قضية جديدة: ${caseData.title}`, 'cases', data.id);
        
        return data;
    }

    async updateCase(caseId, updates) {
        const { data, error } = await this.client
            .from('cases')
            .update(updates)
            .eq('id', caseId)
            .select()
            .single();

        if (error) throw error;
        
        // Log activity
        await this.logActivity('case_updated', `تم تحديث القضية: ${data.title}`, 'cases', caseId);
        
        return data;
    }

    // Appointment methods
    async getAppointments(date = null) {
        if (!this.currentOffice) throw new Error('No current office');

        let query = this.client
            .from('appointments')
            .select(`
                *,
                clients (name),
                cases (title),
                assigned_lawyer:lawyers!appointments_assigned_to_fkey (name)
            `)
            .eq('law_office_id', this.currentOffice.id);

        if (date) {
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);
            
            query = query
                .gte('appointment_date', startOfDay.toISOString())
                .lte('appointment_date', endOfDay.toISOString());
        }

        const { data, error } = await query.order('appointment_date', { ascending: true });
        if (error) throw error;
        return data;
    }

    async createAppointment(appointmentData) {
        if (!this.currentOffice) throw new Error('No current office');

        const { data, error } = await this.client
            .from('appointments')
            .insert({
                ...appointmentData,
                law_office_id: this.currentOffice.id,
                created_by: this.currentUser.id
            })
            .select()
            .single();

        if (error) throw error;
        
        // Log activity
        await this.logActivity('appointment_created', `تم جدولة موعد جديد: ${appointmentData.title}`, 'appointments', data.id);
        
        return data;
    }

    // Activity logging
    async logActivity(type, description, relatedTable = null, relatedId = null) {
        if (!this.currentOffice) return;

        await this.client
            .from('activities')
            .insert({
                law_office_id: this.currentOffice.id,
                user_id: this.currentUser.id,
                activity_type: type,
                description,
                related_table: relatedTable,
                related_id: relatedId
            });
    }

    async getRecentActivities(limit = 10) {
        if (!this.currentOffice) throw new Error('No current office');

        const { data, error } = await this.client
            .from('activities')
            .select(`
                *,
                user:lawyers!activities_user_id_fkey (name)
            `)
            .eq('law_office_id', this.currentOffice.id)
            .order('created_at', { ascending: false })
            .limit(limit);

        if (error) throw error;
        return data;
    }

    // Dashboard statistics
    async getDashboardStats() {
        if (!this.currentOffice) throw new Error('No current office');

        const [clients, cases, todayAppointments, monthlyRevenue] = await Promise.all([
            this.getClientCount(),
            this.getCaseCount(),
            this.getTodayAppointmentCount(),
            this.getMonthlyRevenue()
        ]);

        return {
            clients,
            cases,
            todayAppointments,
            monthlyRevenue
        };
    }

    async getClientCount() {
        const { count, error } = await this.client
            .from('clients')
            .select('*', { count: 'exact', head: true })
            .eq('law_office_id', this.currentOffice.id);

        if (error) throw error;
        return count;
    }

    async getCaseCount() {
        const { count, error } = await this.client
            .from('cases')
            .select('*', { count: 'exact', head: true })
            .eq('law_office_id', this.currentOffice.id)
            .eq('status', 'active');

        if (error) throw error;
        return count;
    }

    async getTodayAppointmentCount() {
        const today = new Date();
        const startOfDay = new Date(today);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(today);
        endOfDay.setHours(23, 59, 59, 999);

        const { count, error } = await this.client
            .from('appointments')
            .select('*', { count: 'exact', head: true })
            .eq('law_office_id', this.currentOffice.id)
            .gte('appointment_date', startOfDay.toISOString())
            .lte('appointment_date', endOfDay.toISOString());

        if (error) throw error;
        return count;
    }

    async getMonthlyRevenue() {
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        const { data, error } = await this.client
            .from('invoices')
            .select('amount')
            .eq('law_office_id', this.currentOffice.id)
            .eq('status', 'paid')
            .gte('paid_date', startOfMonth.toISOString().split('T')[0])
            .lte('paid_date', endOfMonth.toISOString().split('T')[0]);

        if (error) throw error;
        
        return data.reduce((total, invoice) => total + parseFloat(invoice.amount), 0);
    }
}

// Initialize global API instance
window.lawOfficeAPI = new LawOfficeAPI();
