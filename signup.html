<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - نظام إدارة مكاتب المحاماة</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        .signup-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .signup-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        
        .signup-header {
            margin-bottom: 30px;
        }
        
        .signup-header .logo {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .signup-header h1 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 5px;
        }
        
        .signup-header p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 600;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s;
            direction: rtl;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .signup-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 20px;
        }
        
        .signup-btn:hover {
            transform: translateY(-2px);
        }
        
        .signup-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .login-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .login-link:hover {
            text-decoration: underline;
        }
        
        .error-message, .success-message {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        
        .error-message {
            background: #fee;
            color: #c33;
        }
        
        .success-message {
            background: #efe;
            color: #3c3;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <div class="signup-card">
            <div class="signup-header">
                <div class="logo">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <h1>منصة سندان لأعمال المحاماة</h1>
                <p>إنشاء حساب جديد لمكتب المحاماة</p>
            </div>
            
            <form id="signupForm" class="signup-form">
                <div class="error-message" id="errorMessage"></div>
                <div class="success-message" id="successMessage"></div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="lawyerName">اسم المحامي</label>
                        <input type="text" id="lawyerName" name="lawyerName" required>
                    </div>
                    <div class="form-group">
                        <label for="officeName">اسم مكتب المحاماة</label>
                        <input type="text" id="officeName" name="officeName" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">رقم الهاتف</label>
                        <input type="tel" id="phone" name="phone" required>
                    </div>
                    <div class="form-group">
                        <label for="licenseNumber">رقم الترخيص</label>
                        <input type="text" id="licenseNumber" name="licenseNumber">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="address">عنوان المكتب</label>
                    <input type="text" id="address" name="address">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <input type="password" id="password" name="password" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">تأكيد كلمة المرور</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="subscriptionPlan">باقة الاشتراك</label>
                    <select id="subscriptionPlan" name="subscriptionPlan" required>
                        <option value="">اختر الباقة</option>
                        <option value="basic">الباقة الأساسية - 30 د.ك/شهر</option>
                        <option value="advanced">الباقة المتقدمة - 45 د.ك/شهر</option>
                        <option value="professional">الباقة الاحترافية - 65 د.ك/شهر</option>
                    </select>
                </div>
                
                <button type="submit" class="signup-btn" id="signupBtn">
                    إنشاء الحساب
                </button>
                
                <p>
                    لديك حساب بالفعل؟ 
                    <a href="login.html" class="login-link">تسجيل الدخول</a>
                </p>
            </form>
        </div>
    </div>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.me/96555551675" target="_blank" rel="noopener" class="whatsapp-float" title="تواصل معنا عبر واتساب">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth-fix.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            // Initialize API
            await window.lawOfficeAPI.init();
            
            // Check if user is already logged in
            if (window.lawOfficeAPI.currentUser) {
                window.location.href = 'dashboard.html';
                return;
            }
            
            // Handle signup form submission
            const signupForm = document.getElementById('signupForm');
            const signupBtn = document.getElementById('signupBtn');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            
            signupForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // Get form data
                const formData = new FormData(signupForm);
                const data = Object.fromEntries(formData);
                
                // Validate passwords match
                if (data.password !== data.confirmPassword) {
                    errorMessage.textContent = 'كلمات المرور غير متطابقة';
                    errorMessage.style.display = 'block';
                    successMessage.style.display = 'none';
                    return;
                }
                
                // Show loading state
                signupBtn.innerHTML = '<span class="loading"></span> جاري إنشاء الحساب...';
                signupBtn.disabled = true;
                errorMessage.style.display = 'none';
                successMessage.style.display = 'none';
                
                try {
                    // Create user account
                    const userData = {
                        lawyer_name: data.lawyerName,
                        office_name: data.officeName,
                        phone: data.phone,
                        license_number: data.licenseNumber,
                        address: data.address,
                        subscription_plan: data.subscriptionPlan
                    };
                    
                    await window.lawOfficeAPI.signUp(data.email, data.password, userData);
                    
                    // Show success message
                    successMessage.textContent = 'تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني لتأكيد الحساب.';
                    successMessage.style.display = 'block';
                    
                    // Reset form
                    signupForm.reset();
                    
                    // Redirect to login after 3 seconds
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 3000);
                    
                } catch (error) {
                    console.error('Signup error:', error);
                    
                    // Show error message
                    let errorText = 'حدث خطأ في إنشاء الحساب';
                    if (error.message.includes('already registered')) {
                        errorText = 'هذا البريد الإلكتروني مسجل بالفعل';
                    } else if (error.message.includes('Password should be')) {
                        errorText = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                    }
                    
                    errorMessage.textContent = errorText;
                    errorMessage.style.display = 'block';
                    
                } finally {
                    // Reset button
                    signupBtn.innerHTML = 'إنشاء الحساب';
                    signupBtn.disabled = false;
                }
            });
        });
    </script>
</body>
</html>
