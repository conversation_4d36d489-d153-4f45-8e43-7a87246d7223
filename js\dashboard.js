// Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

function initializeDashboard() {
    // Sidebar functionality
    initSidebar();
    
    // Navigation functionality
    initNavigation();
    
    // Search functionality
    initSearch();
    
    // Quick actions
    initQuickActions();
    
    // Period selector
    initPeriodSelector();
    
    // User menu
    initUserMenu();
    
    // Notifications
    initNotifications();
    
    // Real-time updates
    initRealTimeUpdates();
    
    // Animation observers
    initAnimations();
}

// Sidebar functionality
function initSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            
            // Close sidebar when clicking outside on mobile
            if (sidebar.classList.contains('active')) {
                document.addEventListener('click', closeSidebarOnOutsideClick);
            } else {
                document.removeEventListener('click', closeSidebarOnOutsideClick);
            }
        });
    }
    
    function closeSidebarOnOutsideClick(e) {
        if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
            sidebar.classList.remove('active');
            document.removeEventListener('click', closeSidebarOnOutsideClick);
        }
    }
}

// Navigation functionality
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const pageTitle = document.querySelector('.page-title');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to clicked item
            this.parentElement.classList.add('active');
            
            // Update page title
            const linkText = this.querySelector('span').textContent;
            pageTitle.textContent = linkText;
            
            // Load content based on navigation
            loadPageContent(this.getAttribute('href').substring(1));
            
            // Close sidebar on mobile
            if (window.innerWidth <= 1024) {
                document.querySelector('.sidebar').classList.remove('active');
            }
        });
    });
}

// Search functionality
function initSearch() {
    const searchInput = document.querySelector('.search-box input');
    
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length > 2) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            }
        });
    }
}

// Quick actions functionality
function initQuickActions() {
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const actionText = this.querySelector('span').textContent;
            handleQuickAction(actionText);
        });
    });
}

// Handle quick actions
function handleQuickAction(action) {
    switch(action) {
        case 'إضافة عميل جديد':
            showAddClientModal();
            break;
        case 'إنشاء قضية جديدة':
            showAddCaseModal();
            break;
        case 'جدولة موعد':
            showAddAppointmentModal();
            break;
        default:
            showNotification('هذه الميزة قيد التطوير', 'info');
    }
}

// Show add client modal
function showAddClientModal() {
    const modal = createModal('إضافة عميل جديد', `
        <form class="add-client-form">
            <div class="form-row">
                <div class="form-group">
                    <label>الاسم الكامل</label>
                    <input type="text" required>
                </div>
                <div class="form-group">
                    <label>رقم الهاتف</label>
                    <input type="tel" required>
                </div>
            </div>
            <div class="form-group">
                <label>البريد الإلكتروني</label>
                <input type="email">
            </div>
            <div class="form-group">
                <label>العنوان</label>
                <textarea rows="3"></textarea>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">إضافة العميل</button>
                <button type="button" class="btn btn-secondary modal-cancel">إلغاء</button>
            </div>
        </form>
    `);
    
    const form = modal.querySelector('.add-client-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        showNotification('تم إضافة العميل بنجاح', 'success');
        document.body.removeChild(modal);
        updateStats();
    });
}

// Create modal helper function
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.modal-close');
    const cancelBtn = modal.querySelector('.modal-cancel');
    
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    }
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
    
    return modal;
}

// Period selector functionality
function initPeriodSelector() {
    const periodBtns = document.querySelectorAll('.period-btn');
    
    periodBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            periodBtns.forEach(b => b.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Update financial data
            updateFinancialData(this.textContent);
        });
    });
}

// User menu functionality
function initUserMenu() {
    const userMenuToggle = document.querySelector('.user-menu-toggle');
    
    if (userMenuToggle) {
        userMenuToggle.addEventListener('click', function() {
            showUserMenu();
        });
    }
    
    // Logout functionality
    const logoutBtn = document.querySelector('.btn-logout');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            showLogoutConfirmation();
        });
    }
}

// Show logout confirmation
function showLogoutConfirmation() {
    const modal = createModal('تأكيد تسجيل الخروج', `
        <div class="logout-confirmation">
            <p>هل أنت متأكد من رغبتك في تسجيل الخروج؟</p>
            <div class="form-actions">
                <button class="btn btn-danger confirm-logout">تسجيل الخروج</button>
                <button class="btn btn-secondary modal-cancel">إلغاء</button>
            </div>
        </div>
    `);
    
    const confirmBtn = modal.querySelector('.confirm-logout');
    confirmBtn.addEventListener('click', function() {
        showNotification('تم تسجيل الخروج بنجاح', 'success');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
    });
}

// Notifications functionality
function initNotifications() {
    const notificationBtns = document.querySelectorAll('.action-btn');
    
    notificationBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon.classList.contains('fa-bell')) {
                showNotificationsPanel();
            } else if (icon.classList.contains('fa-envelope')) {
                showMessagesPanel();
            }
        });
    });
}

// Real-time updates
function initRealTimeUpdates() {
    setInterval(() => {
        updateNotificationCounts();
    }, 30000);
}

// Update notification counts
function updateNotificationCounts() {
    const notificationCount = document.querySelector('.action-btn .notification-count');
    if (notificationCount) {
        const currentCount = parseInt(notificationCount.textContent);
        const newCount = Math.max(0, currentCount + Math.floor(Math.random() * 3) - 1);
        notificationCount.textContent = newCount;
    }
}

// Update stats
function updateStats() {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        const number = card.querySelector('h3');
        const currentValue = parseInt(number.textContent);
        const newValue = currentValue + 1;
        
        // Animate number change
        animateNumber(number, currentValue, newValue);
    });
}

// Animate number
function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

// Initialize animations
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);
    
    // Observe dashboard cards
    const dashboardCards = document.querySelectorAll('.dashboard-card, .stat-card');
    dashboardCards.forEach(card => {
        card.classList.add('fade-in');
        observer.observe(card);
    });
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">&times;</button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 5000);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    });
}

// Helper functions
function performSearch(query) {
    console.log('البحث عن:', query);
    showNotification(`البحث عن: ${query}`, 'info');
}

function showAddCaseModal() {
    showNotification('فتح نموذج إضافة قضية جديدة', 'info');
}

function showAddAppointmentModal() {
    showNotification('فتح نموذج جدولة موعد جديد', 'info');
}

function updateFinancialData(period) {
    console.log('تحديث البيانات المالية للفترة:', period);
    showNotification(`تم تحديث البيانات للفترة: ${period}`, 'success');
}

function showUserMenu() {
    showNotification('عرض قائمة المستخدم', 'info');
}

function showNotificationsPanel() {
    showNotification('عرض لوحة الإشعارات', 'info');
}

function showMessagesPanel() {
    showNotification('عرض لوحة الرسائل', 'info');
}

function loadPageContent(page) {
    const dashboardContent = document.querySelector('.dashboard-content');
    
    // Show loading state
    dashboardContent.style.opacity = '0.5';
    
    setTimeout(() => {
        console.log('تحميل محتوى الصفحة:', page);
        dashboardContent.style.opacity = '1';
        showNotification(`تم تحميل صفحة ${page}`, 'info');
    }, 500);
}