/* Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: #f8f9fa;
    color: #333;
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* Layout */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    z-index: 1000;
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.main-content {
    margin-right: 280px;
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

/* Sidebar Header */
.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.law-office-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.law-office-logo i {
    font-size: 2.5rem;
    color: #d4af37;
}

.office-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: white;
}

.office-info span {
    font-size: 0.9rem;
    color: #bdc3c7;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-item.active .nav-link {
    background: rgba(212, 175, 55, 0.2);
    color: #d4af37;
    border-right: 3px solid #d4af37;
}

.nav-link i {
    width: 20px;
    margin-left: 15px;
    font-size: 1.1rem;
}

.nav-link .badge {
    margin-right: auto;
    background: #e74c3c;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: #d4af37;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.user-info {
    flex: 1;
}

.user-name {
    display: block;
    font-weight: 600;
    color: white;
    font-size: 0.9rem;
}

.user-role {
    display: block;
    color: #bdc3c7;
    font-size: 0.8rem;
}

.btn-logout {
    background: none;
    border: none;
    color: #bdc3c7;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-logout:hover {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

/* Top Header */
.top-header {
    background: white;
    padding: 15px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: none;
}

.sidebar-toggle:hover {
    background: #f8f9fa;
    color: #2c3e50;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    right: 15px;
    color: #666;
    z-index: 1;
}

.search-box input {
    padding: 10px 45px 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    width: 300px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #f8f9fa;
    color: #2c3e50;
}

.notification-count {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #e74c3c;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-menu-toggle:hover {
    background: #f8f9fa;
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    background: #d4af37;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

/* Dashboard Content */
.dashboard-content {
    padding: 30px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
}

.stat-change.positive {
    background: #d4edda;
    color: #155724;
}

.stat-change.negative {
    background: #f8d7da;
    color: #721c24;
}

.stat-change.neutral {
    background: #fff3cd;
    color: #856404;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.dashboard-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.view-all {
    color: #3498db;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: #2980b9;
}

.btn-add {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.3s ease;
}

.btn-add:hover {
    background: #2980b9;
}

.card-content {
    padding: 25px;
}

/* Case List */
.case-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.case-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.case-item:hover {
    background: #e9ecef;
}

.case-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.case-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.case-date {
    font-size: 0.8rem;
    color: #999;
}

.case-status {
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: flex-start;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.review {
    background: #d1ecf1;
    color: #0c5460;
}

.priority {
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
}

.priority.high {
    background: #f8d7da;
    color: #721c24;
}

.priority.medium {
    background: #fff3cd;
    color: #856404;
}

.priority.low {
    background: #d4edda;
    color: #155724;
}

/* Appointment List */
.appointment-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.appointment-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.appointment-item:hover {
    background: #e9ecef;
}

.appointment-time {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
}

.appointment-time .time {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.appointment-time .duration {
    font-size: 0.8rem;
    color: #666;
}

.appointment-info {
    flex: 1;
}

.appointment-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.appointment-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.location {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    color: #999;
}

.appointment-actions {
    display: flex;
    gap: 5px;
}

.btn-action {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-action:hover {
    background: #e9ecef;
    color: #2c3e50;
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.activity-info p {
    margin-bottom: 5px;
    color: #2c3e50;
    font-size: 0.9rem;
}

.activity-info span {
    display: block;
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.activity-info time {
    color: #999;
    font-size: 0.8rem;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px 15px;
    background: #f8f9fa;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.quick-action-btn:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.quick-action-btn i {
    font-size: 1.5rem;
    color: #3498db;
}

.quick-action-btn span {
    font-size: 0.9rem;
    color: #2c3e50;
    font-weight: 500;
}

/* Financial Overview */
.period-selector {
    display: flex;
    gap: 10px;
}

.period-btn {
    padding: 6px 12px;
    border: 1px solid #e9ecef;
    background: white;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.period-btn.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.period-btn:hover:not(.active) {
    background: #f8f9fa;
}

.financial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.financial-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.financial-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.financial-item:nth-child(1) .financial-icon {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.financial-item:nth-child(2) .financial-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.financial-item:nth-child(3) .financial-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.financial-item:nth-child(4) .financial-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.financial-info h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.financial-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.financial-info .change {
    font-size: 0.8rem;
    font-weight: 600;
}

.change.positive {
    color: #27ae60;
}

.change.negative {
    color: #e74c3c;
}

.change.neutral {
    color: #666;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .search-box input {
        width: 200px;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-content {
        padding: 20px 15px;
    }
    
    .top-header {
        padding: 15px 20px;
    }
    
    .header-right {
        gap: 10px;
    }
    
    .search-box {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .case-item,
    .appointment-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .case-status {
        flex-direction: row;
        gap: 10px;
    }
    
    .appointment-time {
        flex-direction: row;
        gap: 10px;
        min-width: auto;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .financial-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 100%;
    }
    
    .page-title {
        font-size: 1.2rem;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .card-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
}

/* Animations */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(52, 152, 219, 0.3);
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Scrollbar Styling */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* WhatsApp Button Styles */
.whatsapp-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #25D366;
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(37, 211, 102, 0.3);
}

.whatsapp-btn:hover {
    background: #128C7E;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    color: white;
    text-decoration: none;
}

.whatsapp-btn i {
    font-size: 1.2em;
}

/* Floating WhatsApp Button */
.whatsapp-float {
    position: fixed;
    width: 60px;
    height: 60px;
    bottom: 40px;
    left: 40px;
    background-color: #25D366;
    color: white;
    border-radius: 50px;
    text-align: center;
    font-size: 30px;
    box-shadow: 2px 2px 3px #999;
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    background-color: #128C7E;
    transform: scale(1.1);
    color: white;
    text-decoration: none;
}

.whatsapp-float i {
    margin: 0;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}