const express = require('express');
const path = require('path');
const app = express();
const PORT = process.env.PORT || 8080;

// Serve static files
app.use(express.static('.'));

// Default route to start page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'start.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        message: 'منصة سندان لأعمال المحاماة تعمل بنجاح',
        timestamp: new Date().toISOString()
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`
========================================
    منصة سندان لأعمال المحاماة
    Law Office Management System
========================================

🚀 الخادم يعمل على: http://localhost:${PORT}

📋 الصفحات المتاحة:
   • صفحة البداية: http://localhost:${PORT}/start.html
   • الصفحة الرئيسية: http://localhost:${PORT}/index.html
   • تسجيل الدخول: http://localhost:${PORT}/login.html
   • إنشاء حساب: http://localhost:${PORT}/signup.html
   • لوحة التحكم: http://localhost:${PORT}/dashboard.html

💡 لإيقاف الخادم اضغط Ctrl+C
    `);
});
