// Supabase Configuration
const SUPABASE_URL = 'https://anjbnmqgxzhebvfszwaa.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFuamJubXFneHpoZWJ2ZnN6d2FhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDEzODcsImV4cCI6MjA2NDI3NzM4N30.JB0C3zXLdliCnRWJerV_h9azQR4Of1-XwXyfMInLyzU';

// Initialize Supabase client (will be loaded from CDN)
let supabaseClient = null;

// Initialize Supabase when the library is loaded
function initializeSupabase() {
    if (typeof supabase !== 'undefined') {
        supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        console.log('Supabase client initialized successfully');
        return supabaseClient;
    } else {
        console.error('Supabase library not loaded');
        return null;
    }
}

// Database configuration
const DB_CONFIG = {
    url: SUPABASE_URL,
    key: SUPABASE_ANON_KEY,
    tables: {
        law_offices: 'law_offices',
        lawyers: 'lawyers',
        clients: 'clients',
        cases: 'cases',
        appointments: 'appointments',
        documents: 'documents',
        invoices: 'invoices',
        staff: 'staff',
        activities: 'activities'
    }
};

// Application configuration
const APP_CONFIG = {
    name: 'منصة سندان لأعمال المحاماة',
    version: '1.0.0',
    locale: 'ar',
    direction: 'rtl',
    currency: 'KWD',
    timezone: 'Asia/Kuwait',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    contact: {
        whatsapp: '96555551675',
        email: '<EMAIL>',
        website: 'https://phenomenal-faloodeh-de0b81.netlify.app'
    }
};

// API Helper functions
const API = {
    // Get Supabase client
    getClient: () => {
        if (!supabaseClient) {
            supabaseClient = initializeSupabase();
        }
        return supabaseClient;
    },

    // Authentication functions
    auth: {
        signUp: async (email, password, metadata = {}) => {
            const client = API.getClient();
            if (!client) return { error: 'Supabase not initialized' };
            
            const { data, error } = await client.auth.signUp({
                email,
                password,
                options: {
                    data: metadata
                }
            });
            return { data, error };
        },

        signIn: async (email, password) => {
            const client = API.getClient();
            if (!client) return { error: 'Supabase not initialized' };
            
            const { data, error } = await client.auth.signInWithPassword({
                email,
                password
            });
            return { data, error };
        },

        signOut: async () => {
            const client = API.getClient();
            if (!client) return { error: 'Supabase not initialized' };
            
            const { error } = await client.auth.signOut();
            return { error };
        },

        getUser: async () => {
            const client = API.getClient();
            if (!client) return { error: 'Supabase not initialized' };
            
            const { data: { user }, error } = await client.auth.getUser();
            return { user, error };
        }
    },

    // Database functions
    db: {
        // Generic CRUD operations
        select: async (table, columns = '*', filters = {}) => {
            const client = API.getClient();
            if (!client) return { error: 'Supabase not initialized' };
            
            let query = client.from(table).select(columns);
            
            // Apply filters
            Object.entries(filters).forEach(([key, value]) => {
                query = query.eq(key, value);
            });
            
            const { data, error } = await query;
            return { data, error };
        },

        insert: async (table, data) => {
            const client = API.getClient();
            if (!client) return { error: 'Supabase not initialized' };
            
            const { data: result, error } = await client
                .from(table)
                .insert(data)
                .select();
            return { data: result, error };
        },

        update: async (table, id, data) => {
            const client = API.getClient();
            if (!client) return { error: 'Supabase not initialized' };
            
            const { data: result, error } = await client
                .from(table)
                .update(data)
                .eq('id', id)
                .select();
            return { data: result, error };
        },

        delete: async (table, id) => {
            const client = API.getClient();
            if (!client) return { error: 'Supabase not initialized' };
            
            const { error } = await client
                .from(table)
                .delete()
                .eq('id', id);
            return { error };
        }
    }
};

// Export configurations
window.SUPABASE_CONFIG = {
    url: SUPABASE_URL,
    key: SUPABASE_ANON_KEY
};

window.DB_CONFIG = DB_CONFIG;
window.APP_CONFIG = APP_CONFIG;
window.API = API;

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for Supabase library to load
    setTimeout(() => {
        initializeSupabase();
    }, 100);
});
