<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تأكيد الحساب - منصة سندان لأعمال المحاماة</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .confirm-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .confirm-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .confirm-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
        
        .confirm-icon.error {
            color: #dc3545;
        }
        
        .confirm-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .confirm-message {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .confirm-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: transparent;
            color: #3498db;
            border: 2px solid #3498db;
        }
        
        .btn-secondary:hover {
            background: #3498db;
            color: white;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="confirm-container">
        <div class="confirm-card">
            <div id="loading-state" class="loading">
                <div class="spinner"></div>
                <span>جاري تأكيد حسابك...</span>
            </div>
            
            <div id="success-state" style="display: none;">
                <i class="fas fa-check-circle confirm-icon"></i>
                <h1 class="confirm-title">تم تأكيد حسابك بنجاح!</h1>
                <p class="confirm-message">
                    مرحباً بك في منصة سندان لأعمال المحاماة. يمكنك الآن الوصول إلى جميع مميزات النظام.
                </p>
                <div class="confirm-actions">
                    <a href="../dashboard.html" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt"></i>
                        الذهاب للوحة التحكم
                    </a>
                    <a href="../index.html" class="btn btn-secondary">
                        <i class="fas fa-home"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
            
            <div id="error-state" style="display: none;">
                <i class="fas fa-exclamation-triangle confirm-icon error"></i>
                <h1 class="confirm-title">حدث خطأ في التأكيد</h1>
                <p class="confirm-message">
                    عذراً، لم نتمكن من تأكيد حسابك. قد يكون الرابط منتهي الصلاحية أو غير صحيح.
                </p>
                <div class="confirm-actions">
                    <a href="../signup.html" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i>
                        إنشاء حساب جديد
                    </a>
                    <a href="../login.html" class="btn btn-secondary">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/config.js"></script>
    <script>
        // استخراج المعاملات من URL
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        const type = urlParams.get('type');
        const redirectTo = urlParams.get('redirect_to');

        async function confirmEmail() {
            try {
                if (!token || !type) {
                    throw new Error('معاملات غير صحيحة');
                }

                // تأكيد البريد الإلكتروني باستخدام Supabase
                const { data, error } = await supabase.auth.verifyOtp({
                    token_hash: token,
                    type: type
                });

                if (error) {
                    throw error;
                }

                // إخفاء حالة التحميل وإظهار النجاح
                document.getElementById('loading-state').style.display = 'none';
                document.getElementById('success-state').style.display = 'block';

                // إعادة توجيه تلقائية بعد 3 ثوان
                setTimeout(() => {
                    if (redirectTo) {
                        window.location.href = redirectTo;
                    } else {
                        window.location.href = '../dashboard.html';
                    }
                }, 3000);

            } catch (error) {
                console.error('خطأ في تأكيد البريد الإلكتروني:', error);
                
                // إخفاء حالة التحميل وإظهار الخطأ
                document.getElementById('loading-state').style.display = 'none';
                document.getElementById('error-state').style.display = 'block';
            }
        }

        // تشغيل التأكيد عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', confirmEmail);
    </script>
</body>
</html>
