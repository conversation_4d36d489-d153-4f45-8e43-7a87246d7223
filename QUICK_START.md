# 🚀 دليل البداية السريعة - منصة سندان لأعمال المحاماة

## ✅ تم رفع التطبيق بنجاح على Supabase!

تهانينا! تم إعداد وتشغيل نظام إدارة مكاتب المحاماة بنجاح مع جميع المكونات التالية:

### 🗄️ قاعدة البيانات
- ✅ جميع الجداول تم إنشاؤها (مكاتب المحاماة، العملاء، القضايا، المواعيد، الفواتير)
- ✅ Row Level Security (RLS) مُفعل للأمان
- ✅ السياسات الأمنية تم إنشاؤها
- ✅ البيانات التجريبية محملة

### 🔐 نظام المصادقة
- ✅ Supabase Auth مُكوّن
- ✅ تسجيل الدخول والتسجيل يعمل
- ✅ حماية الصفحات مُفعلة

### 🎨 واجهة المستخدم
- ✅ تصميم متجاوب (Responsive)
- ✅ دعم كامل للغة العربية (RTL)
- ✅ واجهة حديثة وسهلة الاستخدام

## 🏃‍♂️ تشغيل التطبيق

### الطريقة الأولى: تشغيل تلقائي
```bash
# في Windows
run.bat

# في Linux/Mac
./run.sh
```

### الطريقة الثانية: تشغيل يدوي
```bash
# تثبيت التبعيات
npm install

# تشغيل الخادم المحلي
npm start
```

## 🌐 الوصول للتطبيق

بعد تشغيل الخادم، يمكنك الوصول للتطبيق عبر:

- **صفحة البداية**: http://localhost:3000/start.html
- **الصفحة الرئيسية**: http://localhost:3000/index.html
- **تسجيل الدخول**: http://localhost:3000/login.html
- **إنشاء حساب**: http://localhost:3000/signup.html
- **لوحة التحكم**: http://localhost:3000/dashboard.html

## 👤 حسابات تجريبية

يمكنك إنشاء حساب جديد أو استخدام البيانات التجريبية:

### مكتب الكندري للمحاماة
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: (يجب إنشاء حساب جديد)

## 📊 البيانات التجريبية

تم تحميل البيانات التجريبية التالية:

### العملاء
- شركة الخليج التجارية
- أحمد محمد علي
- فاطمة سعد الدين
- محمد عبدالله الرشيد
- سارة أحمد الزهراني

### القضايا
- قضية تجارية رقم 2024/123
- قضية عمالية رقم 2024/124
- قضية مدنية رقم 2024/125

### المواعيد
- مواعيد اليوم مع العملاء
- جلسات محكمة مجدولة
- اجتماعات استشارية

## 🔧 إعدادات Supabase

### معلومات المشروع
- **اسم المشروع**: منصة سندان لأعمال المحاماة
- **المعرف**: anjbnmqgxzhebvfszwaa
- **المنطقة**: eu-central-1
- **الحالة**: نشط وجاهز

### الروابط المهمة
- **لوحة تحكم Supabase**: https://app.supabase.com/project/anjbnmqgxzhebvfszwaa
- **قاعدة البيانات**: https://anjbnmqgxzhebvfszwaa.supabase.co
- **API Documentation**: https://anjbnmqgxzhebvfszwaa.supabase.co/rest/v1/

## 🛠️ الملفات المهمة

### ملفات التكوين
- `js/config.js` - إعدادات Supabase والتطبيق
- `js/api.js` - وظائف API للتعامل مع قاعدة البيانات
- `package.json` - تبعيات المشروع

### ملفات HTML
- `index.html` - الصفحة الرئيسية التسويقية
- `login.html` - صفحة تسجيل الدخول
- `signup.html` - صفحة إنشاء حساب جديد
- `dashboard.html` - لوحة التحكم الرئيسية
- `start.html` - صفحة البداية والترحيب

### ملفات JavaScript
- `js/main.js` - وظائف الصفحة الرئيسية
- `js/dashboard.js` - وظائف لوحة التحكم
- `js/config.js` - إعدادات الاتصال بـ Supabase
- `js/api.js` - وظائف API

## 🎯 الخطوات التالية

### 1. اختبار التطبيق
- [ ] إنشاء حساب جديد
- [ ] تسجيل الدخول
- [ ] إضافة عميل جديد
- [ ] إنشاء قضية
- [ ] جدولة موعد
- [ ] عرض التقارير

### 2. التخصيص
- [ ] تغيير الشعار والألوان
- [ ] إضافة معلومات المكتب
- [ ] تخصيص النصوص
- [ ] إضافة مميزات جديدة

### 3. النشر
- [ ] اختيار منصة الاستضافة (Netlify, Vercel, etc.)
- [ ] شراء نطاق مخصص
- [ ] إعداد SSL
- [ ] تفعيل Google Analytics

## 📞 الدعم والمساعدة

### الوثائق
- `README.md` - دليل شامل للمشروع
- `deploy.md` - دليل النشر والاستضافة
- `user-guide.md` - دليل المستخدم
- `marketing-content.md` - المحتوى التسويقي

### التواصل
- **البريد الإلكتروني**: <EMAIL>
- **واتساب**: [96555551675](https://wa.me/96555551675)

## 🎉 تهانينا!

تم إعداد منصة سندان لأعمال المحاماة بنجاح! 

التطبيق جاهز الآن للاستخدام مع:
- ✅ قاعدة بيانات متكاملة
- ✅ نظام مصادقة آمن
- ✅ واجهة مستخدم حديثة
- ✅ بيانات تجريبية للاختبار
- ✅ دعم كامل للغة العربية

**ابدأ الآن بتشغيل التطبيق واستكشاف المميزات!**
