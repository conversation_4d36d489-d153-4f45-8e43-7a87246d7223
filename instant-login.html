<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول فوري - منصة سندان</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .form-group {
            margin: 20px 0;
            text-align: right;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        input:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .links {
            margin-top: 30px;
        }
        
        .links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 تسجيل دخول فوري</h1>
        
        <div id="message" class="message"></div>
        
        <!-- تسجيل دخول بالحساب الموجود -->
        <button class="btn btn-primary" onclick="loginExisting()">
            🔐 تسجيل دخول بالحساب الموجود
        </button>
        
        <!-- إنشاء حساب تجريبي -->
        <button class="btn btn-success" onclick="createTestAccount()">
            ✨ إنشاء حساب تجريبي جديد
        </button>
        
        <!-- تسجيل دخول يدوي -->
        <button class="btn btn-warning" onclick="toggleManualLogin()">
            ⚙️ تسجيل دخول يدوي
        </button>
        
        <!-- نموذج تسجيل دخول يدوي -->
        <div id="manualLogin" style="display: none;">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" placeholder="أدخل البريد الإلكتروني">
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" placeholder="أدخل كلمة المرور">
            </div>
            <button class="btn btn-primary" onclick="manualLogin()">
                تسجيل الدخول
            </button>
        </div>
        
        <div class="links">
            <a href="index.html">العودة للرئيسية</a>
            <a href="debug-login.html">تشخيص المشاكل</a>
        </div>
    </div>

    <!-- تحميل Supabase مباشرة -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <script>
        // إعداد Supabase مباشرة
        const SUPABASE_URL = 'https://anjbnmqgxzhebvfszwaa.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFuamJubXFneHpoZWJ2ZnN6d2FhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDEzODcsImV4cCI6MjA2NDI3NzM4N30.JB0C3zXLdliCnRWJerV_h9azQR4Of1-XwXyfMInLyzU';
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
        }
        
        function hideMessage() {
            document.getElementById('message').style.display = 'none';
        }
        
        async function loginExisting() {
            const email = '<EMAIL>';
            const password = prompt(`أدخل كلمة المرور للحساب: ${email}`);
            
            if (!password) {
                showMessage('❌ يرجى إدخال كلمة المرور', 'error');
                return;
            }
            
            await performLogin(email, password);
        }
        
        async function createTestAccount() {
            const timestamp = Date.now();
            const email = `test${timestamp}@example.com`;
            const password = '123456';
            
            try {
                showMessage('🔄 جاري إنشاء حساب تجريبي...', 'info');
                
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password
                });
                
                if (error) {
                    throw error;
                }
                
                showMessage(`✅ تم إنشاء حساب تجريبي: ${email}`, 'success');
                
                // تسجيل الدخول تلقائياً
                setTimeout(() => {
                    performLogin(email, password);
                }, 2000);
                
            } catch (error) {
                console.error('خطأ في إنشاء الحساب:', error);
                showMessage(`❌ خطأ في إنشاء الحساب: ${error.message}`, 'error');
            }
        }
        
        function toggleManualLogin() {
            const manualDiv = document.getElementById('manualLogin');
            manualDiv.style.display = manualDiv.style.display === 'none' ? 'block' : 'none';
        }
        
        async function manualLogin() {
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showMessage('❌ يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
                return;
            }
            
            await performLogin(email, password);
        }
        
        async function performLogin(email, password) {
            try {
                showMessage('⏳ جاري تسجيل الدخول...', 'info');
                
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) {
                    throw error;
                }
                
                showMessage('✅ تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                
                // التحقق من وجود لوحة التحكم
                setTimeout(() => {
                    // محاولة الانتقال للوحة التحكم
                    const dashboardUrl = 'dashboard.html';
                    
                    // فحص إذا كانت الصفحة موجودة
                    fetch(dashboardUrl, { method: 'HEAD' })
                        .then(response => {
                            if (response.ok) {
                                window.location.href = dashboardUrl;
                            } else {
                                // إذا لم تكن موجودة، انتقل للصفحة الرئيسية
                                showMessage('✅ تم تسجيل الدخول! الانتقال للصفحة الرئيسية...', 'success');
                                setTimeout(() => {
                                    window.location.href = 'index.html';
                                }, 2000);
                            }
                        })
                        .catch(() => {
                            // في حالة الخطأ، انتقل للصفحة الرئيسية
                            window.location.href = 'index.html';
                        });
                }, 1500);
                
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                
                let errorMessage = 'حدث خطأ في تسجيل الدخول';
                
                if (error.message.includes('Invalid login credentials')) {
                    errorMessage = '❌ البريد الإلكتروني أو كلمة المرور غير صحيحة';
                } else if (error.message.includes('Email not confirmed')) {
                    errorMessage = '📧 البريد الإلكتروني غير مؤكد - لكن يمكنك المحاولة مرة أخرى';
                } else if (error.message.includes('Too many requests')) {
                    errorMessage = '⏰ تم تجاوز عدد المحاولات. انتظر دقيقة وحاول مرة أخرى';
                } else {
                    errorMessage = `❌ خطأ: ${error.message}`;
                }
                
                showMessage(errorMessage, 'error');
            }
        }
        
        // فحص المستخدم الحالي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                const { data: { user } } = await supabase.auth.getUser();
                if (user) {
                    showMessage(`✅ أنت مسجل دخول بالفعل كـ: ${user.email}`, 'success');
                    
                    // إضافة زر للانتقال للوحة التحكم
                    const container = document.querySelector('.container');
                    const dashboardBtn = document.createElement('button');
                    dashboardBtn.className = 'btn btn-success';
                    dashboardBtn.textContent = '🎯 الانتقال للوحة التحكم';
                    dashboardBtn.onclick = () => window.location.href = 'dashboard.html';
                    container.appendChild(dashboardBtn);
                }
            } catch (error) {
                console.log('لا يوجد مستخدم مسجل دخول');
            }
        });
    </script>
</body>
</html>
