// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Close mobile menu if open
                if (navMenu.classList.contains('active')) {
                    navMenu.classList.remove('active');
                    navToggle.classList.remove('active');
                }
            }
        });
    });

    // Form Submissions
    const signupForm = document.querySelector('.signup-form');
    const contactForm = document.querySelector('.contact-form form');

    if (signupForm) {
        signupForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleSignupSubmission(this);
        });
    }

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleContactSubmission(this);
        });
    }

    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.feature-card, .pricing-card, .testimonial-card');
    animatedElements.forEach(el => {
        el.classList.add('fade-in');
        observer.observe(el);
    });

    // Header scroll effect
    let lastScrollTop = 0;
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            header.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            header.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });

    // Pricing card interactions
    const pricingCards = document.querySelectorAll('.pricing-card');
    pricingCards.forEach(card => {
        const button = card.querySelector('.btn');
        if (button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const packageName = card.querySelector('h3').textContent;
                showPackageModal(packageName);
            });
        }
    });

    // Dashboard preview interactions
    const sidebarItems = document.querySelectorAll('.sidebar-item');
    sidebarItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all items
            sidebarItems.forEach(i => i.classList.remove('active'));
            // Add active class to clicked item
            this.classList.add('active');
            
            // Update main content based on selection
            updatePreviewContent(this.textContent.trim());
        });
    });
});

// Handle signup form submission
function handleSignupSubmission(form) {
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    
    // Show loading state
    submitButton.innerHTML = '<span class="loading"></span> جاري التسجيل...';
    submitButton.disabled = true;
    
    // Collect form data
    const formData = new FormData(form);
    const data = {
        name: form.querySelector('input[placeholder="اسم المحامي"]').value,
        company: form.querySelector('input[placeholder="اسم مكتب المحاماة"]').value,
        email: form.querySelector('input[placeholder="البريد الإلكتروني"]').value,
        phone: form.querySelector('input[placeholder="رقم الهاتف"]').value
    };
    
    // Simulate API call
    setTimeout(() => {
        // Reset button
        submitButton.textContent = originalText;
        submitButton.disabled = false;
        
        // Show success message
        showNotification('تم التسجيل بنجاح! سيتم التواصل معك قريباً لتفعيل حسابك.', 'success');
        
        // Reset form
        form.reset();
    }, 2000);
}

// Handle contact form submission
function handleContactSubmission(form) {
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    
    // Show loading state
    submitButton.innerHTML = '<span class="loading"></span> جاري الإرسال...';
    submitButton.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Reset button
        submitButton.textContent = originalText;
        submitButton.disabled = false;
        
        // Show success message
        showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', 'success');
        
        // Reset form
        form.reset();
    }, 1500);
}

// Show package selection modal
function showPackageModal(packageName) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>اشتراك في ${packageName}</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>للاشتراك في ${packageName}، يرجى ملء البيانات التالية:</p>
                <form class="package-form">
                    <div class="form-group">
                        <input type="text" placeholder="اسم المحامي" required>
                    </div>
                    <div class="form-group">
                        <input type="text" placeholder="اسم مكتب المحاماة" required>
                    </div>
                    <div class="form-group">
                        <input type="email" placeholder="البريد الإلكتروني" required>
                    </div>
                    <div class="form-group">
                        <input type="tel" placeholder="رقم الهاتف" required>
                    </div>
                    <div class="form-group">
                        <select required>
                            <option value="">اختر طريقة الدفع</option>
                            <option value="knet">الدفع بالكي نت</option>
                            <option value="visa">بطاقة ائتمانية</option>
                            <option value="bank">تحويل بنكي</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary btn-full">تأكيد الاشتراك</button>
                </form>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.modal-close');
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
    
    // Handle form submission
    const packageForm = modal.querySelector('.package-form');
    packageForm.addEventListener('submit', (e) => {
        e.preventDefault();
        handlePackageSubscription(packageForm, packageName);
        document.body.removeChild(modal);
    });
}

// Handle package subscription
function handlePackageSubscription(form, packageName) {
    showNotification(`تم تسجيل طلب الاشتراك في ${packageName} بنجاح! سيتم التواصل معك لإتمام عملية الدفع.`, 'success');
}

// Update dashboard preview content
function updatePreviewContent(section) {
    const previewMain = document.querySelector('.preview-main');
    
    let content = '';
    switch(section) {
        case 'لوحة التحكم':
            content = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">24</div>
                        <div class="stat-label">قضية نشطة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">عميل</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">8</div>
                        <div class="stat-label">موعد اليوم</div>
                    </div>
                </div>
            `;
            break;
        case 'العملاء':
            content = `
                <div class="preview-table">
                    <div class="table-header">قائمة العملاء</div>
                    <div class="table-row">
                        <span>أحمد محمد</span>
                        <span>قضية تجارية</span>
                    </div>
                    <div class="table-row">
                        <span>فاطمة علي</span>
                        <span>قضية عمالية</span>
                    </div>
                    <div class="table-row">
                        <span>خالد سعد</span>
                        <span>قضية مدنية</span>
                    </div>
                </div>
            `;
            break;
        case 'القضايا':
            content = `
                <div class="preview-cases">
                    <div class="case-item">
                        <div class="case-title">قضية رقم 2024/123</div>
                        <div class="case-status active">نشطة</div>
                    </div>
                    <div class="case-item">
                        <div class="case-title">قضية رقم 2024/124</div>
                        <div class="case-status pending">معلقة</div>
                    </div>
                    <div class="case-item">
                        <div class="case-title">قضية رقم 2024/125</div>
                        <div class="case-status completed">مكتملة</div>
                    </div>
                </div>
            `;
            break;
        case 'المواعيد':
            content = `
                <div class="preview-calendar">
                    <div class="calendar-header">مواعيد اليوم</div>
                    <div class="appointment-item">
                        <div class="appointment-time">10:00 ص</div>
                        <div class="appointment-client">اجتماع مع أحمد محمد</div>
                    </div>
                    <div class="appointment-item">
                        <div class="appointment-time">2:00 م</div>
                        <div class="appointment-client">جلسة محكمة - قضية 123</div>
                    </div>
                    <div class="appointment-item">
                        <div class="appointment-time">4:00 م</div>
                        <div class="appointment-client">استشارة قانونية</div>
                    </div>
                </div>
            `;
            break;
    }
    
    previewMain.innerHTML = content;
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">&times;</button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    }, 5000);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        if (document.body.contains(notification)) {
            document.body.removeChild(notification);
        }
    });
}

// Add CSS for dynamic elements
const dynamicStyles = `
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 20px;
    }
    
    .modal-content {
        background: white;
        border-radius: 15px;
        max-width: 500px;
        width: 100%;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .modal-header {
        padding: 20px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        padding: 15px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        z-index: 10001;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    }
    
    .notification-success {
        border-right: 4px solid #27ae60;
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .notification-content i {
        color: #27ae60;
    }
    
    .notification-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        color: #666;
        margin-right: 10px;
    }
    
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .preview-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .table-header {
        background: #f8f9fa;
        padding: 15px;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
    }
    
    .table-row {
        padding: 12px 15px;
        border-bottom: 1px solid #f1f3f4;
        display: flex;
        justify-content: space-between;
    }
    
    .preview-cases {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .case-item {
        background: white;
        padding: 15px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #e9ecef;
    }
    
    .case-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .case-status.active {
        background: #d4edda;
        color: #155724;
    }
    
    .case-status.pending {
        background: #fff3cd;
        color: #856404;
    }
    
    .case-status.completed {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .preview-calendar {
        background: white;
        border-radius: 8px;
        overflow: hidden;
    }
    
    .calendar-header {
        background: #f8f9fa;
        padding: 15px;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
    }
    
    .appointment-item {
        padding: 12px 15px;
        border-bottom: 1px solid #f1f3f4;
        display: flex;
        gap: 15px;
    }
    
    .appointment-time {
        font-weight: 600;
        color: #2c3e50;
        min-width: 80px;
    }
    
    .nav-menu.active {
        display: flex;
        position: absolute;
        top: 100%;
        right: 0;
        left: 0;
        background: white;
        flex-direction: column;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
`;

// Inject dynamic styles
const styleSheet = document.createElement('style');
styleSheet.textContent = dynamicStyles;
document.head.appendChild(styleSheet);