<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - منصة سندان</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            margin-bottom: 20px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .quick-login {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #dee2e6;
        }
        
        .quick-login h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1rem;
        }
        
        .quick-btn {
            width: 100%;
            padding: 10px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        
        .quick-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>🏛️ منصة سندان</h1>
            <p>نظام إدارة مكاتب المحاماة</p>
        </div>
        
        <div id="message" class="message"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn" id="loginBtn">تسجيل الدخول</button>
        </form>
        
        <div class="quick-login">
            <h3>🚀 تسجيل دخول سريع للاختبار</h3>
            <button class="quick-btn" onclick="quickLogin('<EMAIL>')">
                تسجيل دخول بالحساب الموجود
            </button>
            <button class="quick-btn" onclick="createTestAccount()">
                إنشاء حساب تجريبي جديد
            </button>
        </div>
        
        <div class="links">
            <a href="signup.html">إنشاء حساب جديد</a>
            <a href="debug-login.html">تشخيص المشاكل</a>
            <a href="index.html">العودة للرئيسية</a>
        </div>
    </div>

    <script src="js/config.js"></script>
    
    <script>
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
        }
        
        function hideMessage() {
            document.getElementById('message').style.display = 'none';
        }
        
        async function login(email, password) {
            const loginBtn = document.getElementById('loginBtn');
            
            try {
                loginBtn.textContent = '⏳ جاري تسجيل الدخول...';
                loginBtn.disabled = true;
                hideMessage();
                
                if (!window.supabase) {
                    throw new Error('Supabase غير محمل');
                }
                
                const { data, error } = await window.supabase.auth.signInWithPassword({
                    email: email.trim(),
                    password: password
                });
                
                if (error) {
                    throw error;
                }
                
                showMessage('✅ تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
                
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                
                let errorMessage = 'حدث خطأ في تسجيل الدخول';
                
                if (error.message.includes('Invalid login credentials')) {
                    errorMessage = '❌ البريد الإلكتروني أو كلمة المرور غير صحيحة';
                } else if (error.message.includes('Email not confirmed')) {
                    errorMessage = '📧 البريد الإلكتروني غير مؤكد - لكن يمكنك تسجيل الدخول الآن!';
                    // محاولة تسجيل الدخول مرة أخرى
                    setTimeout(() => login(email, password), 2000);
                    return;
                } else if (error.message.includes('Too many requests')) {
                    errorMessage = '⏰ تم تجاوز عدد المحاولات. انتظر دقيقة وحاول مرة أخرى';
                } else {
                    errorMessage = `❌ خطأ: ${error.message}`;
                }
                
                showMessage(errorMessage, 'error');
                
            } finally {
                loginBtn.textContent = 'تسجيل الدخول';
                loginBtn.disabled = false;
            }
        }
        
        async function quickLogin(email) {
            const password = prompt(`أدخل كلمة المرور للحساب: ${email}`);
            if (password) {
                document.getElementById('email').value = email;
                document.getElementById('password').value = password;
                await login(email, password);
            }
        }
        
        async function createTestAccount() {
            const email = `test${Date.now()}@example.com`;
            const password = '123456';
            
            try {
                showMessage('🔄 جاري إنشاء حساب تجريبي...', 'success');
                
                const { data, error } = await window.supabase.auth.signUp({
                    email: email,
                    password: password
                });
                
                if (error) {
                    throw error;
                }
                
                showMessage(`✅ تم إنشاء حساب تجريبي: ${email} | كلمة المرور: ${password}`, 'success');
                
                // ملء النموذج تلقائياً
                document.getElementById('email').value = email;
                document.getElementById('password').value = password;
                
                // تسجيل الدخول تلقائياً بعد 3 ثوان
                setTimeout(() => {
                    login(email, password);
                }, 3000);
                
            } catch (error) {
                showMessage(`❌ خطأ في إنشاء الحساب: ${error.message}`, 'error');
            }
        }
        
        // معالجة النموذج
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showMessage('❌ يرجى إدخال البريد الإلكتروني وكلمة المرور', 'error');
                return;
            }
            
            await login(email, password);
        });
        
        // فحص المستخدم الحالي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                if (window.supabase) {
                    const { data: { user } } = await window.supabase.auth.getUser();
                    if (user) {
                        showMessage('✅ أنت مسجل دخول بالفعل! جاري التوجيه...', 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1500);
                    }
                }
            } catch (error) {
                console.log('لا يوجد مستخدم مسجل دخول');
            }
        });
    </script>
</body>
</html>
