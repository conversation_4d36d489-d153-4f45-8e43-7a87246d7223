// Database Setup Script
// This script sets up the database with sample data for testing

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'https://anjbnmqgxzhebvfszwaa.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFuamJubXFneHpoZWJ2ZnN6d2FhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODcwMTM4NywiZXhwIjoyMDY0Mjc3Mzg3fQ.9m3ZGrRVI6e_uiOUoYB3uJNal5VzREIS8t2mkzJy3Js';

// Initialize Supabase with service key for admin operations
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function setupDatabase() {
    console.log('Setting up database with sample data...');
    
    try {
        // Create sample law office
        const { data: lawOffice, error: officeError } = await supabaseAdmin
            .from('law_offices')
            .insert({
                name: 'مكتب الكندري للمحاماة',
                lawyer_name: '<PERSON>حم<PERSON> الكندري',
                email: '<EMAIL>',
                phone: '+965 2222 3333',
                address: 'الكويت، مدينة الكويت، شارع الخليج العربي',
                license_number: 'LAW-2024-001',
                subscription_plan: 'advanced',
                subscription_status: 'active'
            })
            .select()
            .single();

        if (officeError) {
            console.error('Error creating law office:', officeError);
            return;
        }

        console.log('Law office created:', lawOffice);

        // Create sample clients
        const clients = [
            {
                law_office_id: lawOffice.id,
                name: 'شركة الخليج التجارية',
                email: '<EMAIL>',
                phone: '+965 2444 5555',
                civil_id: '123456789',
                address: 'الكويت، حولي، شارع تونس'
            },
            {
                law_office_id: lawOffice.id,
                name: 'أحمد محمد علي',
                email: '<EMAIL>',
                phone: '+965 9999 8888',
                civil_id: '987654321',
                address: 'الكويت، الجهراء، المنطقة الصناعية'
            },
            {
                law_office_id: lawOffice.id,
                name: 'فاطمة سعد الدين',
                email: '<EMAIL>',
                phone: '+965 7777 6666',
                civil_id: '456789123',
                address: 'الكويت، الفروانية، جليب الشيوخ'
            }
        ];

        const { data: createdClients, error: clientsError } = await supabaseAdmin
            .from('clients')
            .insert(clients)
            .select();

        if (clientsError) {
            console.error('Error creating clients:', clientsError);
            return;
        }

        console.log('Clients created:', createdClients);

        // Create sample cases
        const cases = [
            {
                law_office_id: lawOffice.id,
                client_id: createdClients[0].id,
                case_number: '2024/123',
                title: 'قضية تجارية - نزاع عقد شراكة',
                description: 'نزاع حول شروط عقد الشراكة التجارية',
                case_type: 'تجارية',
                status: 'active',
                priority: 'high',
                court_name: 'محكمة الكويت الكلية',
                judge_name: 'القاضي محمد الرشيد',
                next_hearing_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                law_office_id: lawOffice.id,
                client_id: createdClients[1].id,
                case_number: '2024/124',
                title: 'قضية عمالية - تعويض إصابة عمل',
                description: 'طلب تعويض عن إصابة عمل',
                case_type: 'عمالية',
                status: 'pending',
                priority: 'medium',
                court_name: 'محكمة العمل',
                judge_name: 'القاضية سارة العتيبي',
                next_hearing_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                law_office_id: lawOffice.id,
                client_id: createdClients[2].id,
                case_number: '2024/125',
                title: 'قضية مدنية - نزاع عقاري',
                description: 'نزاع حول ملكية عقار',
                case_type: 'مدنية',
                status: 'review',
                priority: 'low',
                court_name: 'محكمة الكويت الكلية',
                judge_name: 'القاضي خالد المطيري',
                next_hearing_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
            }
        ];

        const { data: createdCases, error: casesError } = await supabaseAdmin
            .from('cases')
            .insert(cases)
            .select();

        if (casesError) {
            console.error('Error creating cases:', casesError);
            return;
        }

        console.log('Cases created:', createdCases);

        // Create sample appointments for today
        const today = new Date();
        const appointments = [
            {
                law_office_id: lawOffice.id,
                client_id: createdClients[1].id,
                case_id: null,
                title: 'اجتماع مع أحمد محمد',
                description: 'استشارة قانونية - قضية تجارية',
                appointment_date: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 10, 0).toISOString(),
                duration: 60,
                location: 'المكتب الرئيسي',
                status: 'scheduled'
            },
            {
                law_office_id: lawOffice.id,
                client_id: createdClients[0].id,
                case_id: createdCases[0].id,
                title: 'جلسة محكمة',
                description: 'قضية رقم 2024/123 - شركة الخليج التجارية',
                appointment_date: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 14, 0).toISOString(),
                duration: 120,
                location: 'محكمة الكويت الكلية',
                status: 'scheduled'
            },
            {
                law_office_id: lawOffice.id,
                client_id: createdClients[2].id,
                case_id: createdCases[2].id,
                title: 'مراجعة مستندات',
                description: 'قضية رقم 2024/125 - فاطمة سعد الدين',
                appointment_date: new Date(today.getFullYear(), today.getMonth(), today.getDate(), 16, 30).toISOString(),
                duration: 45,
                location: 'المكتب الرئيسي',
                status: 'scheduled'
            }
        ];

        const { data: createdAppointments, error: appointmentsError } = await supabaseAdmin
            .from('appointments')
            .insert(appointments)
            .select();

        if (appointmentsError) {
            console.error('Error creating appointments:', appointmentsError);
            return;
        }

        console.log('Appointments created:', createdAppointments);

        // Create sample invoices
        const invoices = [
            {
                law_office_id: lawOffice.id,
                client_id: createdClients[0].id,
                case_id: createdCases[0].id,
                invoice_number: 'INV-2024-001',
                amount: 2500.000,
                currency: 'KWD',
                description: 'أتعاب قانونية - قضية تجارية',
                status: 'paid',
                due_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                paid_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            },
            {
                law_office_id: lawOffice.id,
                client_id: createdClients[1].id,
                case_id: createdCases[1].id,
                invoice_number: 'INV-2024-002',
                amount: 1800.000,
                currency: 'KWD',
                description: 'أتعاب قانونية - قضية عمالية',
                status: 'pending',
                due_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
            }
        ];

        const { data: createdInvoices, error: invoicesError } = await supabaseAdmin
            .from('invoices')
            .insert(invoices)
            .select();

        if (invoicesError) {
            console.error('Error creating invoices:', invoicesError);
            return;
        }

        console.log('Invoices created:', createdInvoices);

        // Create sample activities
        const activities = [
            {
                law_office_id: lawOffice.id,
                activity_type: 'client_created',
                description: 'تم إضافة عميل جديد: شركة الخليج التجارية',
                related_table: 'clients',
                related_id: createdClients[0].id,
                created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()
            },
            {
                law_office_id: lawOffice.id,
                activity_type: 'case_created',
                description: 'تم إنشاء قضية جديدة: قضية تجارية رقم 2024/123',
                related_table: 'cases',
                related_id: createdCases[0].id,
                created_at: new Date(Date.now() - 60 * 60 * 1000).toISOString()
            },
            {
                law_office_id: lawOffice.id,
                activity_type: 'appointment_created',
                description: 'تم جدولة موعد جديد مع أحمد محمد',
                related_table: 'appointments',
                related_id: createdAppointments[0].id,
                created_at: new Date(Date.now() - 120 * 60 * 1000).toISOString()
            },
            {
                law_office_id: lawOffice.id,
                activity_type: 'payment_received',
                description: 'تم استلام دفعة: د.ك 2,500 - فاتورة رقم INV-2024-001',
                related_table: 'invoices',
                related_id: createdInvoices[0].id,
                created_at: new Date(Date.now() - 180 * 60 * 1000).toISOString()
            }
        ];

        const { data: createdActivities, error: activitiesError } = await supabaseAdmin
            .from('activities')
            .insert(activities)
            .select();

        if (activitiesError) {
            console.error('Error creating activities:', activitiesError);
            return;
        }

        console.log('Activities created:', createdActivities);

        console.log('✅ Database setup completed successfully!');
        console.log('Sample law office created with ID:', lawOffice.id);
        console.log('You can now test the application with the sample data.');

    } catch (error) {
        console.error('❌ Error setting up database:', error);
    }
}

// Run setup if this script is executed directly
if (typeof window !== 'undefined') {
    // Browser environment
    window.setupDatabase = setupDatabase;
} else {
    // Node.js environment
    setupDatabase();
}
