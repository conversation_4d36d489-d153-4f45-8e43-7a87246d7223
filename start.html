<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك في منصة سندان لأعمال المحاماة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .welcome-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }
        
        .status {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #4caf50;
        }
        
        .status i {
            margin-left: 10px;
            color: #4caf50;
        }
        
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s;
        }
        
        .action-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .action-btn i {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .action-btn span {
            font-weight: 600;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: right;
        }
        
        .info-card h3 {
            color: #667eea;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }
        
        .info-card h3 i {
            margin-right: 10px;
        }
        
        .info-card ul {
            list-style: none;
        }
        
        .info-card li {
            padding: 5px 0;
            color: #666;
        }
        
        .info-card li:before {
            content: "✓";
            color: #4caf50;
            margin-left: 10px;
        }
        
        .footer {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
        }
        
        .footer a {
            color: #667eea;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .welcome-container {
                padding: 20px;
            }
            
            .actions {
                grid-template-columns: 1fr;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">
            <i class="fas fa-balance-scale"></i>
        </div>
        
        <h1>مرحباً بك في منصة سندان</h1>
        <p class="subtitle">نظام إدارة مكاتب المحاماة الأول في الكويت</p>
        
        <div class="status">
            <i class="fas fa-check-circle"></i>
            <strong>التطبيق جاهز للاستخدام!</strong>
            تم رفع التطبيق بنجاح على Supabase مع قاعدة البيانات والبيانات التجريبية.
        </div>
        
        <div class="actions">
            <a href="index.html" class="action-btn">
                <i class="fas fa-home"></i>
                <span>الصفحة الرئيسية</span>
            </a>
            
            <a href="signup.html" class="action-btn">
                <i class="fas fa-user-plus"></i>
                <span>إنشاء حساب</span>
            </a>
            
            <a href="login.html" class="action-btn">
                <i class="fas fa-sign-in-alt"></i>
                <span>تسجيل الدخول</span>
            </a>
            
            <a href="dashboard.html" class="action-btn">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <h3><i class="fas fa-database"></i>قاعدة البيانات</h3>
                <ul>
                    <li>جميع الجداول تم إنشاؤها</li>
                    <li>Row Level Security مُفعل</li>
                    <li>البيانات التجريبية محملة</li>
                    <li>السياسات الأمنية جاهزة</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3><i class="fas fa-shield-alt"></i>الأمان</h3>
                <ul>
                    <li>تشفير البيانات</li>
                    <li>مصادقة آمنة</li>
                    <li>صلاحيات متدرجة</li>
                    <li>حماية من الهجمات</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3><i class="fas fa-mobile-alt"></i>التوافق</h3>
                <ul>
                    <li>متوافق مع الجوال</li>
                    <li>دعم كامل للعربية</li>
                    <li>واجهة سهلة الاستخدام</li>
                    <li>تحميل سريع</li>
                </ul>
            </div>
            
            <div class="info-card">
                <h3><i class="fas fa-cogs"></i>المميزات</h3>
                <ul>
                    <li>إدارة العملاء والقضايا</li>
                    <li>جدولة المواعيد</li>
                    <li>نظام الفوترة</li>
                    <li>التقارير والإحصائيات</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p>
                تم تطوير هذا النظام بواسطة
                <a href="#" target="_blank">فريق التطوير</a>
            </p>
            <p>
                للدعم الفني:
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
            <p>
                <a href="https://wa.me/96555551675" target="_blank" class="whatsapp-btn" style="display: inline-flex; align-items: center; gap: 8px; background: #25D366; color: white; padding: 10px 15px; border-radius: 25px; text-decoration: none; font-weight: 600; margin-top: 10px;">
                    <i class="fab fa-whatsapp"></i>
                    تواصل معنا عبر واتساب
                </a>
            </p>
        </div>
    </div>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.me/96555551675" target="_blank" rel="noopener" class="whatsapp-float" title="تواصل معنا عبر واتساب">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الظهور التدريجي
            const container = document.querySelector('.welcome-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
            
            // تأثير النقر على الأزرار
            const buttons = document.querySelectorAll('.action-btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // إضافة تأثير النقر
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
            
            // فحص حالة الاتصال بقاعدة البيانات
            checkDatabaseConnection();
        });
        
        async function checkDatabaseConnection() {
            try {
                // محاولة الاتصال بـ Supabase
                const response = await fetch('https://anjbnmqgxzhebvfszwaa.supabase.co/rest/v1/', {
                    headers: {
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFuamJubXFneHpoZWJ2ZnN6d2FhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDEzODcsImV4cCI6MjA2NDI3NzM4N30.JB0C3zXLdliCnRWJerV_h9azQR4Of1-XwXyfMInLyzU'
                    }
                });
                
                if (response.ok) {
                    console.log('✅ الاتصال بقاعدة البيانات ناجح');
                } else {
                    console.warn('⚠️ مشكلة في الاتصال بقاعدة البيانات');
                }
            } catch (error) {
                console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
            }
        }
    </script>
</body>
</html>
