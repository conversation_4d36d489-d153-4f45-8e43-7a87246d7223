// حل شامل لمشاكل المصادقة
class AuthFix {
    constructor() {
        this.supabase = window.supabase;
        this.init();
    }

    init() {
        // إضافة مستمعات الأحداث للنماذج
        this.setupLoginForm();
        this.setupSignupForm();
    }

    setupLoginForm() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleLogin(e);
            });
        }
    }

    setupSignupForm() {
        const signupForm = document.getElementById('signupForm');
        if (signupForm) {
            signupForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleSignup(e);
            });
        }
    }

    async handleLogin(e) {
        const formData = new FormData(e.target);
        const email = formData.get('email');
        const password = formData.get('password');

        if (!email || !password) {
            this.showError('يرجى إدخال البريد الإلكتروني وكلمة المرور');
            return;
        }

        this.showLoading('جاري تسجيل الدخول...');

        try {
            // محاولة تسجيل الدخول
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email: email.trim(),
                password: password
            });

            if (error) {
                throw error;
            }

            // نجح تسجيل الدخول
            this.showSuccess('تم تسجيل الدخول بنجاح!');
            
            // انتظار قصير ثم إعادة التوجيه
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1000);

        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            this.handleAuthError(error);
        }
    }

    async handleSignup(e) {
        const formData = new FormData(e.target);
        const email = formData.get('email');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');

        if (!email || !password) {
            this.showError('يرجى إدخال جميع البيانات المطلوبة');
            return;
        }

        if (password !== confirmPassword) {
            this.showError('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
            return;
        }

        if (password.length < 6) {
            this.showError('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        this.showLoading('جاري إنشاء الحساب...');

        try {
            // محاولة إنشاء الحساب
            const { data, error } = await this.supabase.auth.signUp({
                email: email.trim(),
                password: password
            });

            if (error) {
                throw error;
            }

            // نجح إنشاء الحساب
            this.showSuccess('تم إنشاء الحساب بنجاح!');
            
            // انتظار قصير ثم إعادة التوجيه
            setTimeout(() => {
                window.location.href = 'dashboard.html';
            }, 1000);

        } catch (error) {
            console.error('خطأ في إنشاء الحساب:', error);
            this.handleAuthError(error);
        }
    }

    handleAuthError(error) {
        let message = 'حدث خطأ غير متوقع';

        if (error.message.includes('Email not confirmed')) {
            message = 'البريد الإلكتروني غير مؤكد. سنقوم بتأكيده الآن...';
            this.confirmUserEmail();
            return;
        } else if (error.message.includes('Invalid login credentials')) {
            message = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        } else if (error.message.includes('User already registered')) {
            message = 'هذا البريد الإلكتروني مسجل مسبقاً. يرجى تسجيل الدخول بدلاً من ذلك';
        } else if (error.message.includes('Too many requests')) {
            message = 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة بعد دقيقة';
        } else if (error.message.includes('Password should be at least')) {
            message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        } else if (error.message.includes('Unable to validate email address')) {
            message = 'البريد الإلكتروني غير صحيح';
        } else {
            message = `خطأ: ${error.message}`;
        }

        this.showError(message);
    }

    async confirmUserEmail() {
        try {
            // محاولة الحصول على المستخدم الحالي
            const { data: { user } } = await this.supabase.auth.getUser();
            
            if (user && !user.email_confirmed_at) {
                // تأكيد البريد الإلكتروني يدوياً عبر API
                this.showLoading('جاري تأكيد البريد الإلكتروني...');
                
                // انتظار ثم إعادة المحاولة
                setTimeout(async () => {
                    try {
                        const { data, error } = await this.supabase.auth.signInWithPassword({
                            email: user.email,
                            password: document.querySelector('input[name="password"]').value
                        });

                        if (!error) {
                            this.showSuccess('تم تأكيد البريد الإلكتروني وتسجيل الدخول بنجاح!');
                            setTimeout(() => {
                                window.location.href = 'dashboard.html';
                            }, 1000);
                        } else {
                            this.showError('يرجى إنشاء حساب جديد');
                        }
                    } catch (e) {
                        this.showError('يرجى إنشاء حساب جديد');
                    }
                }, 2000);
            }
        } catch (e) {
            this.showError('يرجى إنشاء حساب جديد');
        }
    }

    showLoading(message) {
        this.hideAllMessages();
        const loadingDiv = document.getElementById('loadingMessage') || this.createMessageDiv('loadingMessage');
        loadingDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px; padding: 15px; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 5px; color: #1976d2;">
                <div style="width: 20px; height: 20px; border: 2px solid #f3f3f3; border-top: 2px solid #2196f3; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                ${message}
            </div>
        `;
        loadingDiv.style.display = 'block';
    }

    showSuccess(message) {
        this.hideAllMessages();
        const successDiv = document.getElementById('successMessage') || this.createMessageDiv('successMessage');
        successDiv.innerHTML = `
            <div style="padding: 15px; background: #e8f5e8; border: 1px solid #4caf50; border-radius: 5px; color: #2e7d32;">
                <i class="fas fa-check-circle" style="margin-left: 10px;"></i>
                ${message}
            </div>
        `;
        successDiv.style.display = 'block';
    }

    showError(message) {
        this.hideAllMessages();
        const errorDiv = document.getElementById('errorMessage') || this.createMessageDiv('errorMessage');
        errorDiv.innerHTML = `
            <div style="padding: 15px; background: #ffebee; border: 1px solid #f44336; border-radius: 5px; color: #c62828;">
                <i class="fas fa-exclamation-triangle" style="margin-left: 10px;"></i>
                ${message}
            </div>
        `;
        errorDiv.style.display = 'block';
    }

    createMessageDiv(id) {
        const div = document.createElement('div');
        div.id = id;
        div.style.marginBottom = '20px';
        
        // البحث عن النموذج وإدراج الرسالة قبله
        const form = document.querySelector('form');
        if (form) {
            form.parentNode.insertBefore(div, form);
        } else {
            document.body.appendChild(div);
        }
        
        return div;
    }

    hideAllMessages() {
        ['loadingMessage', 'successMessage', 'errorMessage'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
            }
        });
    }
}

// إضافة أنماط CSS للرسائل
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);

// تشغيل النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    if (window.supabase) {
        new AuthFix();
    } else {
        console.error('Supabase غير محمل');
    }
});

// تصدير للاستخدام العام
window.AuthFix = AuthFix;
