<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب - منصة سندان</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .signup-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
        }
        
        input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            margin-bottom: 20px;
        }
        
        .btn:hover {
            background: #218838;
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #b3d9ff;
            color: #0066cc;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="signup-container">
        <div class="logo">
            <h1>🏛️ منصة سندان</h1>
            <p>إنشاء حساب جديد</p>
        </div>
        
        <div class="info">
            ✨ <strong>مبروك!</strong> لا تحتاج لتأكيد البريد الإلكتروني. سيتم تفعيل حسابك فوراً!
        </div>
        
        <div id="message" class="message"></div>
        
        <form id="signupForm">
            <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور (6 أحرف على الأقل)</label>
                <input type="password" id="password" name="password" required minlength="6">
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">تأكيد كلمة المرور</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            
            <button type="submit" class="btn" id="signupBtn">إنشاء الحساب</button>
        </form>
        
        <div class="links">
            <a href="simple-login.html">لديك حساب؟ تسجيل الدخول</a>
            <a href="index.html">العودة للرئيسية</a>
        </div>
    </div>

    <script src="js/config.js"></script>
    
    <script>
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
        }
        
        function hideMessage() {
            document.getElementById('message').style.display = 'none';
        }
        
        async function signup(email, password) {
            const signupBtn = document.getElementById('signupBtn');
            
            try {
                signupBtn.textContent = '⏳ جاري إنشاء الحساب...';
                signupBtn.disabled = true;
                hideMessage();
                
                if (!window.supabase) {
                    throw new Error('Supabase غير محمل');
                }
                
                const { data, error } = await window.supabase.auth.signUp({
                    email: email.trim(),
                    password: password
                });
                
                if (error) {
                    throw error;
                }
                
                showMessage('✅ تم إنشاء الحساب بنجاح! جاري التوجيه للوحة التحكم...', 'success');
                
                // مسح النموذج
                document.getElementById('signupForm').reset();
                
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);
                
            } catch (error) {
                console.error('خطأ في إنشاء الحساب:', error);
                
                let errorMessage = 'حدث خطأ في إنشاء الحساب';
                
                if (error.message.includes('User already registered')) {
                    errorMessage = '❌ هذا البريد الإلكتروني مسجل مسبقاً. جرب تسجيل الدخول';
                } else if (error.message.includes('Password should be at least')) {
                    errorMessage = '❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                } else if (error.message.includes('Unable to validate email address')) {
                    errorMessage = '❌ البريد الإلكتروني غير صحيح';
                } else if (error.message.includes('Signup is disabled')) {
                    errorMessage = '❌ التسجيل معطل حالياً';
                } else {
                    errorMessage = `❌ خطأ: ${error.message}`;
                }
                
                showMessage(errorMessage, 'error');
                
            } finally {
                signupBtn.textContent = 'إنشاء الحساب';
                signupBtn.disabled = false;
            }
        }
        
        // معالجة النموذج
        document.getElementById('signupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (!email || !password || !confirmPassword) {
                showMessage('❌ يرجى ملء جميع الحقول', 'error');
                return;
            }
            
            if (password.length < 6) {
                showMessage('❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
                return;
            }
            
            if (password !== confirmPassword) {
                showMessage('❌ كلمات المرور غير متطابقة', 'error');
                return;
            }
            
            await signup(email, password);
        });
        
        // فحص المستخدم الحالي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                if (window.supabase) {
                    const { data: { user } } = await window.supabase.auth.getUser();
                    if (user) {
                        showMessage('✅ أنت مسجل دخول بالفعل! جاري التوجيه...', 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1500);
                    }
                }
            } catch (error) {
                console.log('لا يوجد مستخدم مسجل دخول');
            }
        });
    </script>
</body>
</html>
