<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة مكاتب المحاماة</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .login-header {
            margin-bottom: 30px;
        }
        
        .login-header .logo {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .login-header h1 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 5px;
        }
        
        .login-header p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 600;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s;
            direction: rtl;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 20px;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .signup-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .signup-link:hover {
            text-decoration: underline;
        }
        
        .error-message {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <h1>منصة سندان لأعمال المحاماة</h1>
                <p>تسجيل الدخول إلى لوحة التحكم</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="error-message" id="errorMessage"></div>
                
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    تسجيل الدخول
                </button>
                
                <p>
                    ليس لديك حساب؟ 
                    <a href="signup.html" class="signup-link">إنشاء حساب جديد</a>
                </p>
            </form>
        </div>
    </div>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.me/96555551675" target="_blank" rel="noopener" class="whatsapp-float" title="تواصل معنا عبر واتساب">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            // Initialize API
            await window.lawOfficeAPI.init();
            
            // Check if user is already logged in
            if (window.lawOfficeAPI.currentUser) {
                window.location.href = 'dashboard.html';
                return;
            }
            
            // Handle login form submission
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');
            
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                
                // Show loading state
                loginBtn.innerHTML = '<span class="loading"></span> جاري تسجيل الدخول...';
                loginBtn.disabled = true;
                errorMessage.style.display = 'none';
                
                try {
                    // Attempt login
                    await window.lawOfficeAPI.signIn(email, password);
                    
                    // Redirect to dashboard on success
                    window.location.href = 'dashboard.html';
                    
                } catch (error) {
                    console.error('Login error:', error);
                    
                    // Show error message
                    let errorText = 'حدث خطأ في تسجيل الدخول';
                    if (error.message.includes('Invalid login credentials')) {
                        errorText = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                    } else if (error.message.includes('Email not confirmed')) {
                        errorText = 'يرجى تأكيد البريد الإلكتروني أولاً';
                    }
                    
                    errorMessage.textContent = errorText;
                    errorMessage.style.display = 'block';
                    
                } finally {
                    // Reset button
                    loginBtn.innerHTML = 'تسجيل الدخول';
                    loginBtn.disabled = false;
                }
            });
        });
    </script>
</body>
</html>
